# Repurpose Content API Documentation

## Overview

The `repurpose_content` operation is designed to transform existing content into authentically human-written material that bypasses AI detection while maintaining the original message and similar length. This endpoint follows the established content generation module patterns for consistency with other API operations.

## Endpoint Configuration

- **Endpoint**: `/process` (POST)
- **Operation Name**: `repurpose_content`
- **Processing Type**: Process endpoint only

## Request Format

### Required Fields

```json
{
    "data": {
        "content": "string"  // Required: The original content to be repurposed
    },
    "operation": "repurpose_content"
}
```

### Optional Fields

```json
{
    "data": {
        "content": "string",           // Required
        "tone": "string",              // Optional: Default "professional"
        "target_audience": "string",   // Optional: Default "general"
        "model_name": "string"         // Optional: Azure OpenAI model selection
    },
    "operation": "repurpose_content"
}
```

## Response Format

### Success Response

```json
{
    "result": [
        {
            "text": "Repurposed content that maintains the original message while being authentically human-written..."
        }
    ],
    "status": "success",
    "message": "Operation 'repurpose_content' completed successfully"
}
```

### Error Response

```json
{
    "detail": "Error message describing what went wrong"
}
```

## Content Generation Requirements

### Human Authenticity Features

- **Natural Language Patterns**: Uses conversational language with natural imperfections
- **Authentic Voice**: Incorporates genuine emotional expressions and personal touches
- **Anti-AI Detection**: Varies sentence structure, uses human speech patterns, includes natural hesitations
- **Length Maintenance**: Keeps approximately the same length as original content
- **Clean Output**: No prefacing text or formatting separators

### Quality Standards

1. **Bypass AI Detection**: Content is designed to pass AI detection tools
2. **Maintain Core Message**: Preserves all key information and main points
3. **Similar Length**: Maintains approximately the same word count as input
4. **Avoid Corporate Speak**: Eliminates AI patterns and overly polished language
5. **Ready for Use**: Output is clean and ready for immediate posting/use

## Technical Implementation

### Azure OpenAI Integration

- **LiteLLM Interface**: Uses unified interface for Azure OpenAI services
- **Model Support**: Compatible with all configured Azure OpenAI deployments
- **Fallback Handling**: Graceful fallback for missing model deployments
- **Parameter Priority**: Follows established parameter priority logic

### Helicone Logging

All API calls are automatically logged to Helicone with proper headers:
- `Helicone-Auth: Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva`
- `Helicone-OpenAI-Api-Base`

### Model Selection

- **Default Model**: Uses recommended model for content rewriting if none specified
- **Supported Models**: All Azure OpenAI models (gpt-4o, gpt-4.1, grok-3, llama-3.3, deepseek, etc.)
- **Intelligent Routing**: Automatically routes to appropriate Azure endpoints

## Usage Examples

### Basic Usage

```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "content": "Artificial intelligence is transforming business operations. Companies are implementing AI solutions to automate processes and improve efficiency."
    },
    "operation": "repurpose_content"
  }'
```

### Advanced Usage with Custom Parameters

```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "content": "Our new product launch exceeded expectations with 150% growth in the first quarter.",
      "tone": "engaging",
      "target_audience": "business professionals",
      "model_name": "azure/gpt-4o"
    },
    "operation": "repurpose_content"
  }'
```

### Python Client Example

```python
import requests

def repurpose_content(content, tone="professional", target_audience="general"):
    payload = {
        "data": {
            "content": content,
            "tone": tone,
            "target_audience": target_audience
        },
        "operation": "repurpose_content"
    }
    
    response = requests.post(
        "http://localhost:8000/process",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        return result["result"][0]["text"]
    else:
        raise Exception(f"API call failed: {response.text}")

# Usage
original = "AI is changing how we work and live in unprecedented ways."
repurposed = repurpose_content(original, tone="engaging", target_audience="tech professionals")
print(repurposed)
```

## Validation Rules

### Required Field Validation

- `content`: Must be present and non-empty string
- Content cannot be just whitespace

### Optional Field Defaults

- `tone`: Defaults to "professional"
- `target_audience`: Defaults to "general"
- `model_name`: Uses recommended model if not specified

## Error Handling

### Common Error Scenarios

1. **Missing Content**: Returns 400 error if content field is missing
2. **Empty Content**: Returns 400 error if content is empty or whitespace only
3. **Model Unavailable**: Gracefully falls back to alternative models
4. **API Timeout**: Handles long-running requests with appropriate timeouts

### Error Response Format

```json
{
    "detail": "Missing required fields: content"
}
```

## Integration with Existing Systems

### MongoDB Storage

- Automatically stores repurposed content in MongoDB following existing patterns
- Operation type: "Repurpose_Content"
- Includes metadata: original content snippet, tone, target audience

### Monitoring and Analytics

- All requests logged to Helicone for monitoring
- Performance metrics tracked via ModelManager
- Error rates and fallback usage monitored

## Best Practices

1. **Content Length**: Works best with content between 50-500 words
2. **Clear Instructions**: Provide specific tone and audience for better results
3. **Model Selection**: Use gpt-4o or gpt-4.1 for best quality results
4. **Batch Processing**: For multiple content pieces, make separate API calls
5. **Error Handling**: Always implement proper error handling in client code

## Comparison with Other Operations

| Feature | repurpose_content | short_content | long_content |
|---------|------------------|---------------|--------------|
| Input Type | Existing content | Topic/prompt | Topic/prompt |
| Output Length | Similar to input | 80-100 words | 200-300 words |
| Primary Use | Content transformation | New content creation | Detailed content creation |
| AI Detection | Optimized to bypass | Standard generation | Standard generation |
| Human Touch | Maximum authenticity | Professional quality | Professional quality |
