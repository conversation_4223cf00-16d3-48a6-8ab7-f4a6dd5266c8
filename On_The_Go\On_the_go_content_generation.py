# import libraries
import random
import os
from litellm import completion
import time
import json
import re
from post_processing_layer.post_proc import *
from Model_call.call_model import *


# topic list given by customer
topic_list = [
"""showcases your expertise in [your industry] by providing a useful tip or insight that can help others in your field.""",

"""Share a concise, actionable tip or tool that has improved productivity, learning, or specific skills in [your area of expertise]. For example, "Here’s a quick trick for boosting efficiency in [tool/skill].""",

"""ask an open-ended question related to [your industry] or [your niche] that can spark a meaningful discussion with your audience.""",

"""Share a “5 Things You Need to Know About [Trending Topic or Skill in Industry]” to quickly educate your audience on an essential aspect or change in your field.""",

"""reveals a common misconception or myth in [interests/industry] and explains why it is false or misleading.""",

"""Offer insights on an emerging trend or technology in [your industry]** and discuss potential impacts or opportunities it might create. This showcases your forward-thinking approach and invites your audience to think ahead.""",

"""recommends a book, podcast, or course that you found valuable or inspiring for your {interest/industry}. (for same user, can the recommendations be made different?)""",

"""Discuss a real-world problem currently impacting [your industry] and propose a hypothetical innovative solution.** Invite your audience to brainstorm additional creative solutions or share experiences related to tackling similar challenges.""",

"""invites your audience to share their opinions or feedback on a topic related to {interest/industry}.""",

"""Highlight an inspirational story of a leader or innovator in [your industry] who has made a significant impact,** and discuss the lessons or traits that can be applied to everyday professional life. Ask your audience who inspires them and why.""",

"""Create a “Did You Know?” post featuring an interesting fact or lesser-known aspect of [your industry],** and encourage comments or additional facts from your audience. Facts can pique interest and prompt information sharing.""",

"""Pose a "What If?" scenario about a hypothetical change or innovation in [your industry],** and discuss its potential effects. Encourage your audience to brainstorm and share how they would adapt or leverage the change.""",

"""Reflect on a piece of advice you received early in your career that’s stuck with you,** and invite your audience to share the best advice they’ve ever gotten, creating a thread of wisdom and encouragement."""
]


# modify this by API logic
# trending topic list
industry_topic_list = ["Cloud Computing", "Generative AI", "Data Science", "Machine Learning", "Quantum Computing"]


# Prompts defination of all 13 topic, based on the list given by the customer.
def prompt_1(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice,
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional,
          engaging, and tailored to the industry audience.

    """

    INDUSTRY = select_random_topic(industry_topic_list)
    prompt_content = f"""
    Write a LinkedIn post as someone who works in {INDUSTRY} and has just discovered something useful that they're excited to share with their network.

    🎯 HUMAN WRITING APPROACH:
    - Write like you're texting a colleague who asked for advice
    - Share something you've actually learned or experienced recently
    - Use "I" statements and personal experiences ("I've been using...", "I discovered...", "What worked for me...")
    - Include small imperfections that make it feel real (like starting a sentence with "And" or "But")
    - Sound genuinely helpful, not like you're trying to sell something

    📝 CONTENT STYLE:
    - Start with a relatable problem or "aha moment"
    - Share one specific, actionable tip that actually works
    - Keep it conversational - like you're talking, not writing a manual
    - End with genuine curiosity about others' experiences
    - 40-60 words max - quick and punchy

    🚫 AVOID THESE AI RED FLAGS:
    - "In today's digital landscape" or similar corporate speak
    - "Unlock," "leverage," "optimize," "streamline" - these scream AI
    - Perfect grammar and formal structure
    - Generic advice that could apply to anyone
    - Ending with "What are your thoughts?" (too obvious)

    💡 MAKE IT SCROLL-STOPPING:
    - Start with something surprising or counterintuitive
    - Use specific numbers, tools, or examples
    - Include a small personal detail or story
    - Make people think "I should try that" or "I didn't know that"

    Write about sharing a practical {INDUSTRY} tip or insight that has genuinely helped you. Make it feel like insider knowledge you're sharing with friends.

    Example tone: "Been struggling with [specific problem]? Found something that actually works..." or "Quick tip that saved me hours this week..."
    """

    return prompt_content

def prompt_2(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice,
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional,
          engaging, and tailored to the industry audience.

    """


    AREA_OF_EXPERTISE = select_random_topic(industry_topic_list)
    prompt_content = f"""
    Write a LinkedIn post as someone in {AREA_OF_EXPERTISE} who just found a tool or trick that made their work way easier and wants to share it.

    🎯 AUTHENTIC SHARING STYLE:
    - Write like you're genuinely excited about something that just saved you time
    - Start with the problem you were having, then reveal the solution
    - Use casual language like "So I was struggling with..." or "Found this thing that..."
    - Include specific details (tool names, time saved, exact results)
    - Sound like you're doing a favor for your network, not showing off

    💬 CONVERSATIONAL TONE:
    - Use contractions (I've, don't, can't) to sound natural
    - Include small hesitations or qualifiers ("I think," "seems like," "might be worth trying")
    - Add personal context ("been doing this for years but...")
    - End with genuine curiosity, not a generic question

    🔥 MAKE IT VALUABLE:
    - Share something specific and actionable
    - Include numbers or measurable results when possible
    - Mention why this matters for {AREA_OF_EXPERTISE} work specifically
    - Give enough detail that someone could actually try it
    - 50-70 words - enough to be helpful, short enough to read quickly

    🚫 AVOID SOUNDING LIKE AI:
    - Don't use "game-changer," "revolutionary," or "cutting-edge"
    - Skip formal introductions or explanations
    - No perfect bullet points or structured lists
    - Don't end with "thoughts?" or "what do you think?"
    - Avoid overly polished language

    Write about a productivity tip, tool, or technique in {AREA_OF_EXPERTISE} that genuinely helped you. Make it feel like insider knowledge you're sharing because you want to help others avoid the same struggle.

    Example approach: "Spent way too long doing [task] manually until I found [specific solution]. Now it takes 5 minutes instead of 2 hours. If you're dealing with [same problem], this might help..."
    """
    return prompt_content

def prompt_3(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice,
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional,
          engaging, and tailored to the industry audience.

    """

    INDUSTRY_NICHE = select_random_topic(industry_topic_list)
    prompt_content = f"""
    Write a LinkedIn post as someone in {INDUSTRY_NICHE} who's genuinely curious about how others handle a specific challenge or approach.

    🤔 AUTHENTIC CURIOSITY STYLE:
    - Start with something you've been thinking about or debating
    - Share your current approach first, then ask how others do it
    - Use phrases like "I've been wondering..." or "Curious how others handle..."
    - Make it feel like you're genuinely interested in learning, not just generating engagement
    - Include a bit of context about why this matters

    💭 NATURAL QUESTION FLOW:
    - Set up the scenario briefly (1-2 sentences)
    - Share what you currently do or what you've observed
    - Ask the question in a conversational way
    - Maybe mention why you're asking (new project, recent experience, etc.)
    - 40-60 words total - concise but thoughtful

    🎯 MAKE IT RELATABLE:
    - Focus on real challenges people in {INDUSTRY_NICHE} actually face
    - Use specific terminology that shows you know the field
    - Avoid generic questions that could apply to any industry
    - Make people think "Oh yeah, I deal with that too"

    🚫 AVOID ENGAGEMENT BAIT:
    - Don't use "Agree?" or "Thoughts?" - too obvious
    - Skip "What's your experience?" - overused
    - Avoid questions with obvious answers
    - Don't make it sound like a survey or poll

    Write about a genuine dilemma, approach, or decision point in {INDUSTRY_NICHE} work. Make it feel like you're asking colleagues for their perspective because you actually want to know.

    Example approach: "Been going back and forth on [specific approach] vs [alternative]. Currently doing [your method] but wondering if [other way] might be better for [specific situation]. How do you usually handle this?"
    """

    return prompt_content

def prompt_4(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """

    INDUSTRY = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {INDUSTRY} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Share a “5 Things You Need to Know About {INDUSTRY}” to quickly educate your audience on an essential aspect or change in {INDUSTRY} field.
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {INDUSTRY} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {INDUSTRY} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags
    
       Example:
       For a post about AI in Healthcare:
        ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
        ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {INDUSTRY} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection
       
    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {INDUSTRY} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {INDUSTRY} professional would genuinely want to read, share, and learn from.

    Output Example:
    5 Things You Need to Know About Data Science:

    1. Data is the new oil. Embrace a data-driven mindset to uncover valuable insights. 
    2. Master data wrangling. Clean, preprocess, and structure data for optimal analysis. 
    3. Explore machine learning algorithms. Understand their strengths and limitations. 
    4. Visualize insights effectively. Communicate findings through compelling visuals. 
    5. Stay curious and keep learning. Data science is an ever-evolving field.

    #DataAnalytics #DataViz #DataScience #MachineLearning #LifelongLearning 
    
    """

    return prompt_content

def prompt_5(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """


    interest_industry = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {interest_industry} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Reveals a common misconception or myth in {interest_industry} and explains why it is false or misleading.
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {interest_industry} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {interest_industry} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags

        Example:
            For a post about AI in Healthcare:
                ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
                ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)


    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {interest_industry} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection

    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone
        
    Execution Template:
    [Specific {interest_industry} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {interest_industry} professional would genuinely want to read, share, and learn from.


    Output Example:
    Misconception: Quantum computing will soon replace classical computing. 
    Reality? Quantum computers excel at specialized tasks like optimization and simulation, but classical computers remain essential for general-purpose computing. 
    Combining both technologies is key for tackling complex challenges.

    #HybridComputing #InnovationStrategy #QuantumComputing 

"""
    return prompt_content

def prompt_6(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """


    specific_industry = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {specific_industry} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Offer insights on an emerging trend or technology in {specific_industry} and discuss potential impacts or opportunities it might create. 
       - This showcases your forward-thinking approach and invites your audience to think ahead.
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {specific_industry} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {specific_industry} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags

        Example:
        For a post about AI in Healthcare:
            ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
            ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {specific_industry} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection

    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone
        
    Execution Template:
    [Specific {specific_industry} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {specific_industry} professional would genuinely want to read, share, and learn from.


    Output Example:
    Generative AI is rapidly transforming how we approach data analysis and model development. Rather than manually coding every step, AI models can now generate code, visualizations, and insights autonomously. This frees data scientists to focus on higher-level tasks, accelerating innovation. 🚀
    Leverage generative AI to streamline your workflow - start by exploring open-source tools like OpenAI's GPT models or Google's PaLM. Identify repetitive tasks that could be automated, then iteratively train and refine your AI assistant. You'll gain a force-multiplier for productivity.
    #AIProdutivity #DataScience #FutureOfWork #GenerativeAI #MachineLearning 

    """
    return prompt_content

def prompt_7(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """


    interest_industry = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {interest_industry} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Recommends a book, podcast, or course that you found valuable or inspiring for {interest_industry} industry. 
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {interest_industry} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {interest_industry} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags

        Example:
        For a post about AI in Healthcare:
            ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
            ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {interest_industry} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection
       
    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {interest_industry} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {interest_industry} professional would genuinely want to read, share, and learn from.


    Output Example:
    Mastering cloud architecture? Check out the "AWS Certified Solutions Architect" course by Andrew Brown. It demystifies complex cloud concepts through real-world examples. The course builds a solid foundation for designing resilient, secure, and high-performing cloud solutions. 🚀
    As a cloud pro, staying ahead of rapid tech shifts is crucial. Brown's course equips you with practical strategies to optimize cloud infrastructure, ensuring scalability and cost-efficiency. Whether you're prepping for certification or leveling up your cloud skills, this resource is a game-changer. 💡
    Don't just take my word for it - thousands of learners rave about Brown's ability to make the complex feel simple. Invest in this course and unlock your true cloud potential today.

    #CloudComputing #SkillsDevelopment #CloudArchitecture #TechEducation #ProfessionalGrowth #AWSCertification

    """
    
    return prompt_content

def prompt_8(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """
     
    INDUSTRY = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {INDUSTRY} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Discuss a real-world problem currently impacting {INDUSTRY} and propose a hypothetical innovative solution.
       - Invite your audience to brainstorm additional creative solutions or share experiences related to tackling similar challenges.
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {INDUSTRY} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {INDUSTRY} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags
    
       Example:
       For a post about AI in Healthcare:
        ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
        ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {INDUSTRY} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection
       
    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {INDUSTRY} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {INDUSTRY} professional would genuinely want to read, share, and learn from.

    Output Example:
    Scaling AI models on cloud infrastructure: A balancing act between cost and performance. 
    Optimizing resource utilization through right-sizing, auto-scaling, and spot instances can significantly reduce costs while maintaining high performance.
    #CloudComputing #CostOptimization #AIOps #MachineLearning  

    """

    return prompt_content

def prompt_9(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """

    interest_industry = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {interest_industry} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Invites your audience to share their opinions or feedback on a topic related to {interest_industry}
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {interest_industry} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {interest_industry} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags

        Example:
        For a post about AI in Healthcare:
            ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
            ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {interest_industry} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection
       
    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {interest_industry} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {interest_industry} professional would genuinely want to read, share, and learn from.

    Output Example:
    As an expert content writer for Generative AI professionals, here's an authentic, value-driven LinkedIn post that provides real insights:
    Embracing Generative AI? Start with a solid data foundation. Clean, structured data is key for accurate model training and reliable outputs. Audit your datasets regularly for quality assurance. 💡📊
    #AIProfessionals #GenerativeAI #MachineLearning #DataQuality  

    """
    return prompt_content

def prompt_10(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """

    INDUSTRY =  select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {INDUSTRY} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Highlight an inspirational story of a leader or innovator in {INDUSTRY} who has made a significant impact, and discuss the lessons or traits that can be applied to everyday professional life. 
       - Ask your audience who inspires them and why
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {INDUSTRY} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {INDUSTRY} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags
    
       Example:
       For a post about AI in Healthcare:
        ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
        ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {INDUSTRY} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection

    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {INDUSTRY} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {INDUSTRY} professional would genuinely want to read, share, and learn from.

    Output Example:
    Indra Nooyi's leadership at PepsiCo teaches a powerful lesson - adapt or become obsolete. As CEO, she championed innovation by investing in healthier products and sustainable practices. Her vision? "What is good for our people and planet is also good for PepsiCo." 💡🌱
    In the rapidly evolving Generative AI landscape, staying ahead means constantly upskilling and embracing change. Like Nooyi, identify emerging trends and evolve your expertise. An open mindset unlocks new opportunities. 
    Who inspires your professional growth? Share an innovator whose vision motivates you to adapt and thrive.
    #TechLeadership #DataDriven #InnovationCulture #FutureOfWork #DigitalTransformation  

    """
    return prompt_content


def prompt_11(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """

    specific_industry =  select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {specific_industry} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Create a “Did You Know?” post featuring an interesting fact or lesser-known aspect of {specific_industry} and encourage comments or additional facts from your audience. 
       - Facts can pique interest and prompt information sharing
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {specific_industry} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {specific_industry} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags

        Example:
        For a post about AI in Healthcare:
            ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
            ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {specific_industry} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection

    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {specific_industry} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {specific_industry} professional would genuinely want to read, share, and learn from.


    Output Example:
    Did you know that Generative AI models like GPT-3 can be fine-tuned for specific tasks like text summarization, question answering, and even code generation? 🤖💻 
    This opens up exciting possibilities for streamlining workflows and augmenting human intelligence in various domains. 
    Share your thoughts on the potential applications of fine-tuned language models in your field.
    #GenerativeAI #NLP #AIMagic #FutureTech #InnovationUnleashed

    """
    return prompt_content

def prompt_12(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """
     
    your_industry = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {your_industry} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Pose a "What If?" scenario about a hypothetical change or innovation in {your_industry}, and discuss its potential effects. 
       - Encourage your audience to brainstorm and share how they would adapt or leverage the change. 
       - Facts can pique interest and prompt information sharing
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {your_industry} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {your_industry} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags

        Example:
        For a post about AI in Healthcare:
            ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
            ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {your_industry} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection

    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {your_industry} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {your_industry} professional would genuinely want to read, share, and learn from.


    Output Example:
    What if quantum computing revolutionized data encryption and security protocols? As quantum tech evolves, we may need to rethink how we protect sensitive information. 
    How would you adapt your data practices to leverage quantum-resistant encryption? 🔐🔑
    Share your thoughts on preparing for this quantum shift. The future of secure data depends on innovating today. 
    #QuantumComputing #DataSecurity #Encryption #CyberSecurity #TechFuture

    """
    return prompt_content

def prompt_13(industry_topic_list):
    """
    Generates a detailed prompt for creating LinkedIn posts tailored to a randomly selected industry.

    Args:
        industry_topic_list (list): A list of industry topics to select from.

    Returns:
        str: A structured and comprehensive prompt providing guidelines for writing a professional 
             LinkedIn post targeted at the selected industry.

    Function Workflow:
        - Randomly selects an industry from the provided `industry_topic_list` using `select_random_topic`.
        - Constructs a prompt with clear and specific writing frameworks, tone guidelines, structural advice, 
          language precision, hashtag strategy, and emoji usage.
        - The generated prompt serves as a guide for creating LinkedIn posts that are professional, 
          engaging, and tailored to the industry audience.

    """

    your_industry = select_random_topic(industry_topic_list)
    prompt_content = f"""
    You are an expert content writer creating LinkedIn posts for {your_industry} professionals. Your mission: Generate authentic, value-driven content that provides real insights.

    Core Writing Framework:
    1. Content Generation Logic:
       - Reflect on a piece of advice you received early in your {your_industry} career that’s stuck with you, and invite your audience to share the best advice they’ve ever gotten, 
       - creating a thread of wisdom and encouragement 
       - Facts can pique interest and prompt information sharing
       - Prioritize readability and immediate understanding
       - Use short, impactful sentences
       - Employ action-oriented language
       - Ensure every sentence concludes with a full stop

    2. Tone and Communication Strategy:
       - Write as an experienced {your_industry} professional
       - Sound like you're sharing a quick, valuable tip with a trusted colleague
       - Use human-like, accessible vocabulary
       - Keep language simple and direct
       - Communicate complex ideas in straightforward terms

    3. Structural Guidelines:
       - Craft content within 160 words
       - Focus on one clear, valuable insight
       - Create an immediate connection with the reader
       - Demonstrate practical knowledge

    4. Language Precision:
       Strictly Prohibited Words/Phrases:
       - Overused openers: "In today's world," "In the world of," "In today's era"
       - Cliché action words: "Unlock," "unleash," "unveil," "unravel," "uncover"
       - Hyperbolic descriptors: "Critical," "crucial," "essential," "it's important to"
       - Dramatic transitions: "Delve," "embark on a journey," "pave the way"
       - Redundant closings: "In conclusion," "in summary," "ultimately"
       - Unnecessary connectors: "Furthermore," "moreover," "additionally"
       - Overused descriptive terms: "Bustling," "vibrant," "hustle and bustle"
       - Pretentious vocabulary: "Tapestry," "Multifaceted," "Foster," "Paramount"

    5. Hashtag Strategy:
       - Generate only 2-3 unique highly relevant hashtags
       - Do not repeat the hashtags in same post at any cost
       - Focus on specific {your_industry} topics
       - Use professional, industry-specific tags
       - Avoid generic or overly broad hashtags

        Example:
        For a post about AI in Healthcare:
            ✅ Correct: #AIinHealthcare, #DigitalHealthInnovation, #HealthTechTrends
            ❌ Incorrect: #AI, #Healthcare, #AIinHealthcare (repeated), #Tech (too broad)

    6. Content Essence:
       - Provide a single, clear professional insight
       - Demonstrate practical {your_industry} expertise
       - Offer a perspective that adds immediate value
       - Create a sense of professional connection

    7. Emoji Strategy:

        - Select 1-2 professional, context-appropriate emojis
        - Emojis must directly relate to the content's core message
        - Prioritize emojis that enhance emotional connection
        - Acceptable emoji categories:
            * Professional achievement: 🏆, 📈, 💡
            * Learning & growth: 🌱, 🚀, 📊
            * Networking & connection: 🤝, 💬, 🌐
            * Industry-specific symbolic representation

        - Placement: Strategically used within or at the end of the post
        - Avoid overused or generic emojis
        - Ensure emojis complement, not overshadow, the professional tone

    Execution Template:
    [Specific {your_industry} Insight] + [Practical Professional Tip] + [Actionable Perspective] + [Relevant Industry Hashtags]

    Primary Objective: Generate a content that a {your_industry} professional would genuinely want to read, share, and learn from.

    Output Example:
    One of the best pieces of advice I received early in my data science career: "Always be learning." 
    Our field evolves rapidly - new tools, techniques and discoveries emerge constantly. Staying curious and committed to continuous learning is vital. 
    What's something you've recently learned that's impacted how you approach your work? I'll start - exploring generative AI has shifted how I think about augmenting data pipelines. 
    Now it's your turn to share a recent learning moment. Let's create a thread celebrating the mindset of lifelong learners.

    #CloudComputing #ProfessionalDevelopment #TechInsights #QuantumComputing #DataScience #GenerativeAI #MachineLearning  

"""
    return prompt_content




# Choose n number of random topic from the topic list provided by customer
def random_promtps_selection(topic_list, number_of_topic):
    """
    Select a specified number of random topics from the provided topic list and return their indices.

    Args:
        topic_list (list): A list of topics provided by the customer.
        number_of_topic (int): The number of topics to randomly select.

    Returns:
        list: A list of indices corresponding to the randomly selected topics in the original topic list.
    """


    number_of_topic = int(number_of_topic)
    # Choose 10 random topic from the topic list
    random_selection = random.sample(topic_list, number_of_topic)
    index_list = []
    for i in random_selection:
        # Find the index of the item
        index = topic_list.index(i)
        index_list.append(index)
        # print("Index ", index)
    return index_list


# Choose 1 random tredning industry topic from the list
def select_random_topic(industry_topic_list):
    """
    Select one random trending industry topic from the provided list.

    Args:
        industry_topic_list (list): A list of trending industry topics.

    Returns:
        one randomly selected topic from the input list.
    """
    # Choose 1 random tredning industry topic from the list
    random_selection = random.sample(industry_topic_list, 1)
    return random_selection



# Main Function to call all the inter-related function
def on_the_go_promtps_selection(industry_topic_list, number_of_topic,  model_name = None):
    """
    Generate and return a JSON-formatted string of content based on randomly selected topics 
    and processed by a specified model.

    Args:
        topic_list (list): A list of topics to select prompts from.
        number_of_topic (int): The number of topics to randomly select for generating prompts.
        model_name (str, optional): The name of the model to use for processing prompts. 
            Defaults to None, which uses the default model.

    Returns:
        str: A JSON-formatted string containing the generated content. Each entry in the JSON 
             is a dictionary with a "text" key holding the content generated by the model.

    Notes:
        - The function randomly selects indices from `topic_list` using `random_promtps_selection`.
        - Based on the selected index, a corresponding prompt function (e.g., `prompt_1`, `prompt_2`) is called.
        - Each prompt is processed by the `model_call_for_on_the_go_feature` function to generate content.
        - The output content is compiled into a JSON string with proper formatting.
        - If an invalid index is encountered, the function prints a message and skips further processing.

    Raises:
        ValueError: If `number_of_topic` exceeds the length of `topic_list`.

    Example:
        ```python
        topic_list = ["AI", "Cloud Computing", "Data Science", "Blockchain"]
        result = on_the_go_promtps_selection(topic_list, 3, model_name="gpt-4")
        print(result)
        # Output (formatted JSON string):
        # [
        #     {"text": "Generated content 1"},
        #     {"text": "Generated content 2"},
        #     {"text": "Generated content 3"}
        # ]
        ```
    """

    on_the_go_content_list = []
    index_list = random_promtps_selection(topic_list, number_of_topic)
    for i in index_list:
        i = int(i)
        if i == 0:
            prompts = prompt_1(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 1:
            prompts = prompt_2(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 2:
            prompts = prompt_3(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 3:
            prompts = prompt_4(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 4:
            prompts = prompt_5(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 5:
            prompts = prompt_6(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 6:
            prompts = prompt_7(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 7:
            prompts = prompt_8(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 8:
            prompts = prompt_9(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 9:
            prompts = prompt_10(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 10:
            prompts = prompt_11(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 11:
            prompts = prompt_12(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        elif i == 12:
            prompts = prompt_13(industry_topic_list)
            model_res = model_call_for_on_the_go_feature(prompts, model_name)
            on_the_go_content_list.append(model_res)
        else:
            print("In None condition")
            
    # Convert list to JSON format
    json_data = [{"text": text} for text in on_the_go_content_list]

    # Output JSON as a string
    json_output = json.dumps(json_data, ensure_ascii=False, indent=4)

    # Parse the raw JSON string
    parsed_result = json.loads(json_output)

    # Extract and clean the text content
    cleaned_texts = [{"text": item["text"]} for item in parsed_result]

    return cleaned_texts
