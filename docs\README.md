# Enhanced FastAPI Documentation

This directory contains comprehensive documentation for the Enhanced FastAPI application with LiteLLM Azure OpenAI integration and Helicone monitoring capabilities.

## 📚 Documentation Structure

### Core Documentation
- **[API Reference](api_reference.md)** - Complete endpoint documentation with examples
- **[Model Guide](model_guide.md)** - Detailed model capabilities and selection criteria  
- **[Integration Guide](integration_guide.md)** - Step-by-step developer integration
- **[Helicone Setup](helicone_setup.md)** - Monitoring configuration and usage

### Support Documentation
- **[Troubleshooting](troubleshooting.md)** - Common issues and solutions
- **[Examples](examples/)** - Code samples and use cases

## 🚀 Quick Start

1. **API Overview**: Start with [API Reference](api_reference.md) for endpoint details
2. **Model Selection**: Use [Model Guide](model_guide.md) for choosing the right model
3. **Integration**: Follow [Integration Guide](integration_guide.md) for implementation
4. **Monitoring**: Set up [Helicone](helicone_setup.md) for observability

## 🔧 Testing Resources

- **[Postman Collection](../postman_collection.json)** - Ready-to-use API testing collection
- **[Test Examples](examples/)** - Sample requests and responses

## 📊 Supported Models

The application supports 8 Azure OpenAI models across two endpoints:

### Advanced Models (East US 2)
- `azure/gpt-4.1` - Latest GPT-4 with enhanced capabilities
- `azure/o1` - Advanced reasoning model

### Standard Models (West Europe)  
- `azure/gpt-4o` - Optimized GPT-4 variant
- `azure/grok-3` - Grok model for creative tasks
- `azure/llama-3.3` - Meta's Llama model
- `azure/deepseek` - DeepSeek model for technical content
- `azure/gpt-4o-mini` - Lightweight GPT-4 variant
- `azure/gpt-35-turbo` - Fast, cost-effective model

## 🔍 Key Features

- **Intelligent Model Routing** - Automatic selection based on task complexity
- **Dual-Endpoint Architecture** - Optimized for performance and cost
- **Fallback Mechanisms** - Robust error handling and recovery
- **Helicone Monitoring** - Comprehensive observability and analytics
- **Cost Optimization** - Smart model selection for budget efficiency
- **Real-time Health Checks** - Continuous endpoint monitoring

## 📞 Support

For issues or questions:
1. Check [Troubleshooting Guide](troubleshooting.md)
2. Review [API Reference](api_reference.md) for endpoint details
3. Test with [Postman Collection](../postman_collection.json)
