import time
from litellm import completion
import os
import json
import re
from post_processing_layer.post_proc import *
from Model_call.call_model import *
from dotenv import load_dotenv

# Load environment variables from the .env file
load_dotenv()


# prompts instruction for without framework - default
def advanced_content_generator_default(topic, target_audience, tone):
    """
    Sophisticated content generation framework with nuanced emotional intelligence
    
    :param topic: Core subject of the content
    :param target_audience: Specific professional or demographic group
    :param tone: Emotional and communication style
    """
    # Tone Intelligence Mapping
    TONE_INTELLIGENCE = {
        'Creative': {
            'core_essence': "Inspiring innovation and original thinking",
            'language_markers': [
                "Reimagine", "Breakthrough", "Innovative approach",
                "Unconventional wisdom", "Creative disruption"
            ],
            'emotional_undertone': "Excitement about possibilities"
        },
        'Curious': {
            'core_essence': "Exploring ideas with genuine wonder",
            'language_markers': [
                "What if", "Imagine", "Discover", "Exploring the frontier",
                "Uncovering insights"
            ],
            'emotional_undertone': "Intellectual excitement and openness"
        },
        'Empathetic': {
            'core_essence': "Deep understanding and emotional connection",
            'language_markers': [
                "I understand", "Walking in your shoes", "Shared experience",
                "Connecting deeply", "Truly hearing you"
            ],
            'emotional_undertone': "Compassionate support"
        },
        'Casual': {
            'core_essence': "Approachable and conversational",
            'language_markers': [
                "Hey there", "Let's talk", "Real talk", "Just between us",
                "No fancy words"
            ],
            'emotional_undertone': "Friendly and relatable"
        },
        'Formal': {
            'core_essence': "Professional and precise communication",
            'language_markers': [
                "Furthermore", "Consequently", "Hereby", "Aforementioned",
                "Substantive analysis"
            ],
            'emotional_undertone': "Authoritative and credible"
        },
        'Fun': {
            'core_essence': "Lighthearted and enjoyable engagement",
            'language_markers': [
                "Guess what", "Playful insight", "Fun fact", "Laugh-out-loud moment",
                "Entertaining twist"
            ],
            'emotional_undertone': "Playful joy"
        },
        'Happy': {
            'core_essence': "Radiating positive energy and optimism",
            'language_markers': [
                "Exciting breakthrough", "Wonderful opportunity", "Delightful discovery",
                "Incredible journey", "Amazing potential"
            ],
            'emotional_undertone': "Pure joy and enthusiasm"
        },
        'Hopeful': {
            'core_essence': "Inspiring potential and positive futures",
            'language_markers': [
                "Promising horizon", "Emerging potential", "Path forward",
                "Transformative possibility", "Bright future ahead"
            ],
            'emotional_undertone': "Optimistic anticipation"
        },
        'Informal': {
            'core_essence': "Conversational and relaxed communication",
            'language_markers': [
                "So", "Basically", "Right?", "Check this out", "Thing is..."
            ],
            'emotional_undertone': "Comfortable and authentic"
        },
        'Informative': {
            'core_essence': "Clear, precise knowledge transfer",
            'language_markers': [
                "Key insights", "Critical understanding", "Fundamental principle",
                "Evidence suggests", "Research indicates"
            ],
            'emotional_undertone': "Intellectual clarity"
        },
        'Joyful': {
            'core_essence': "Celebrating life and positive experiences",
            'language_markers': [
                "Absolutely amazing", "Thrilling moment", "Celebration of",
                "Pure excitement", "Incredible journey"
            ],
            'emotional_undertone': "Unbridled happiness"
        },
        'Optimistic': {
            'core_essence': "Positive outlook despite challenges",
            'language_markers': [
                "Potential breakthrough", "Promising approach", "Opportunity ahead",
                "Positive trajectory", "Transformative potential"
            ],
            'emotional_undertone': "Hopeful resilience"
        },
        'Persuasive': {
            'core_essence': "Compelling argument and influence",
            'language_markers': [
                "Consider this", "Imagine the impact", "Key advantage",
                "Strategic insight", "Compelling evidence"
            ],
            'emotional_undertone': "Confident conviction"
        },
        'Sarcastic': {
            'core_essence': "Witty critique through ironic commentary",
            'language_markers': [
                "Oh, sure", "Absolutely brilliant", "What could possibly go wrong",
                "Clearly a genius move", "Totally revolutionary"
            ],
            'emotional_undertone': "Sardonic amusement"
        },
        'Sincere': {
            'core_essence': "Genuine and heartfelt communication",
            'language_markers': [
                "From the heart", "Truly believe", "Honest insight",
                "Genuine perspective", "Deeply committed"
            ],
            'emotional_undertone': "Authentic vulnerability"
        },
        'Witty': {
            'core_essence': "Intelligent humor and clever wordplay",
            'language_markers': [
                "Clever twist", "Unexpected insight", "Playful perspective",
                "Humorous take", "Brilliantly observed"
            ],
            'emotional_undertone': "Intellectual amusement"
        }
    }

    # Prompt Generation Logic
    def generate_prompt(topic, target_audience, tone):
        tone_data = TONE_INTELLIGENCE.get(tone, TONE_INTELLIGENCE['Informative'])
        
        prompt = f"""
        CONTENT GENERATION DIRECTIVE:

        TONE PROFILE:
        - Emotional Core: {tone_data['core_essence']}
        - Communication Style: Embody the {tone} tone for {target_audience}
        - Topic Expertise: {topic} insights with {tone} emotional intelligence

        LANGUAGE CALIBRATION:
        - Incorporate Tone Markers: {" | ".join(tone_data['language_markers'])}
        - Use first-person perspective (e.g., "I’ve learned," "In my experience").
        - Maintain a professional yet conversational voice.
        - Be authentic, vulnerable, and relatable—like talking to a trusted colleague.

        EMOTIONAL RESONANCE:
        - Core Emotional Undertone: {tone_data['emotional_undertone']}
        - Start with a strong emotional hook to immediately engage the reader.

        FORMAT STRUCTURE:
        1. **Opening Hook (1-2 lines)**:
        - Begin with a personal insight, question, or bold statement.
        - Avoid generic phrases like "Key Insights" or "Let’s talk about."

        2. **Main Content (2-3 short paragraphs)**:
        - Share a story, experience, or specific example related to the topic.
        - Break content into short, scannable paragraphs (2-3 lines max).
        - Use clear line breaks between sections for readability.

        3. **Key Elements (if needed)**:
        - Use bullet points sparingly (max 3-4) for emphasis.
        - Ensure bullet points are concise and impactful.

        4. **Closing Thought/Call to Action**:
        - End with a reflection, question, or invitation for discussion.
        - Encourage engagement (e.g., "What’s your take?" or "Let’s discuss!").

        5. **Hashtags**:
        - Include 2-3 unique, relevant hashtags in ProperCase (e.g., #LeadershipSkills, #CareerGrowth).
        - Avoid repeating hashtags at all costs.

        TECHNICAL SPECS:
        - **Word Count**: 300-350 words (longer, more detailed post).
        - **Emojis**: Use 1-2 professional emojis strategically (e.g., in the hook or closing).
        - **F-Shaped Layout**:
        - First two lines: Strong hook.
        - First paragraph: Key message.
        - Subsequent paragraphs: Supporting details.
        - Bullet points (if used): Place them early for scanning.
        - **Spacing**: Ensure clear line breaks between sections for visual appeal.

        CONTENT MUST:
        - Begin with a personal, relatable insight.
        - Use complete, natural sentences.
        - Flow conversationally—avoid robotic or overly formal language.
        - Include specific examples, experiences, or anecdotes.
        - End with a thoughtful reflection or call to action.

        AVOID:
        - Corporate jargon (e.g., "leverage," "synergy").
        - Generic motivation (e.g., "Work hard, dream big").
        - Buzzwords (e.g., "disruption," "pivot").
        - Exaggerated claims (e.g., "This will change everything!").
        - Bullet-heavy content (max 3-4 bullets if absolutely necessary).
        - Directive-style openings (e.g., "Here are 5 tips...").

        STRATEGIC CONSTRAINTS:
        - Eliminate generic corporate language.
        - Prioritize genuine, nuanced communication.
        - Reflect deep understanding of {target_audience}'s context.

        OUTPUT REQUIREMENT:
        Produce a piece of content that feels like a {tone} conversation about {topic}, 
        speaking directly to the heart of {target_audience}'s professional experience. 
        The post should be engaging, well-structured, and optimized for LinkedIn’s F-shaped reading pattern.
        """
        return prompt

    return generate_prompt(topic, target_audience, tone)


# prompts instruction for Framework
def advanced_content_generator_with_framework(topic, target_audience, tone, framework):
    """
    Advanced LinkedIn content generation framework with enhanced human-like content creation.
    
    Args:
        topic (str): Core subject of the content
        target_audience (str): Specific professional or demographic group
        tone (str): Emotional and communication style
        framework (str): Content creation framework

    Returns:
        str: Optimized LinkedIn content generation prompt
    """

    # Tone Intelligence Mapping
    TONE_INTELLIGENCE = {
        'Creative': {
            'core_essence': "Inspiring innovation and original thinking",
            'language_markers': [
                "Reimagine", "Breakthrough", "Innovative approach",
                "Unconventional wisdom", "Creative disruption"
            ],
            'emotional_undertone': "Excitement about possibilities"
        },
        'Curious': {
            'core_essence': "Exploring ideas with genuine wonder",
            'language_markers': [
                "What if", "Imagine", "Discover", "Exploring the frontier",
                "Uncovering insights"
            ],
            'emotional_undertone': "Intellectual excitement and openness"
        },
        'Empathetic': {
            'core_essence': "Deep understanding and emotional connection",
            'language_markers': [
                "I understand", "Walking in your shoes", "Shared experience",
                "Connecting deeply", "Truly hearing you"
            ],
            'emotional_undertone': "Compassionate support"
        },
        'Casual': {
            'core_essence': "Approachable and conversational",
            'language_markers': [
                "Hey there", "Let's talk", "Real talk", "Just between us",
                "No fancy words"
            ],
            'emotional_undertone': "Friendly and relatable"
        },
        'Formal': {
            'core_essence': "Professional and precise communication",
            'language_markers': [
                "Furthermore", "Consequently", "Hereby", "Aforementioned",
                "Substantive analysis"
            ],
            'emotional_undertone': "Authoritative and credible"
        },
        'Fun': {
            'core_essence': "Lighthearted and enjoyable engagement",
            'language_markers': [
                "Guess what", "Playful insight", "Fun fact", "Laugh-out-loud moment",
                "Entertaining twist"
            ],
            'emotional_undertone': "Playful joy"
        },
        'Happy': {
            'core_essence': "Radiating positive energy and optimism",
            'language_markers': [
                "Exciting breakthrough", "Wonderful opportunity", "Delightful discovery",
                "Incredible journey", "Amazing potential"
            ],
            'emotional_undertone': "Pure joy and enthusiasm"
        },
        'Hopeful': {
            'core_essence': "Inspiring potential and positive futures",
            'language_markers': [
                "Promising horizon", "Emerging potential", "Path forward",
                "Transformative possibility", "Bright future ahead"
            ],
            'emotional_undertone': "Optimistic anticipation"
        },
        'Informal': {
            'core_essence': "Conversational and relaxed communication",
            'language_markers': [
                "So", "Basically", "Right?", "Check this out", "Thing is..."
            ],
            'emotional_undertone': "Comfortable and authentic"
        },
        'Informative': {
            'core_essence': "Clear, precise knowledge transfer",
            'language_markers': [
                "Key insights", "Critical understanding", "Fundamental principle",
                "Evidence suggests", "Research indicates"
            ],
            'emotional_undertone': "Intellectual clarity"
        },
        'Joyful': {
            'core_essence': "Celebrating life and positive experiences",
            'language_markers': [
                "Absolutely amazing", "Thrilling moment", "Celebration of",
                "Pure excitement", "Incredible journey"
            ],
            'emotional_undertone': "Unbridled happiness"
        },
        'Optimistic': {
            'core_essence': "Positive outlook despite challenges",
            'language_markers': [
                "Potential breakthrough", "Promising approach", "Opportunity ahead",
                "Positive trajectory", "Transformative potential"
            ],
            'emotional_undertone': "Hopeful resilience"
        },
        'Persuasive': {
            'core_essence': "Compelling argument and influence",
            'language_markers': [
                "Consider this", "Imagine the impact", "Key advantage",
                "Strategic insight", "Compelling evidence"
            ],
            'emotional_undertone': "Confident conviction"
        },
        'Sarcastic': {
            'core_essence': "Witty critique through ironic commentary",
            'language_markers': [
                "Oh, sure", "Absolutely brilliant", "What could possibly go wrong",
                "Clearly a genius move", "Totally revolutionary"
            ],
            'emotional_undertone': "Sardonic amusement"
        },
        'Sincere': {
            'core_essence': "Genuine and heartfelt communication",
            'language_markers': [
                "From the heart", "Truly believe", "Honest insight",
                "Genuine perspective", "Deeply committed"
            ],
            'emotional_undertone': "Authentic vulnerability"
        },
        'Witty': {
            'core_essence': "Intelligent humor and clever wordplay",
            'language_markers': [
                "Clever twist", "Unexpected insight", "Playful perspective",
                "Humorous take", "Brilliantly observed"
            ],
            'emotional_undertone': "Intellectual amusement"
        }
    }
    

    # Enhanced Framework Templates for Social Media Optimization
    FRAMEWORKS = {
        "PAS": {
            "description": "Problem-Agitation-Solution framework for compelling content",
            "social_media_optimization": "Perfect for addressing pain points and presenting solutions",
            "template": """
            🚨 {problem_hook}

            Here's what's really happening: {agitation_detail}

            The solution? {solution_insight}

            What's your experience with this challenge?
            """,
            "problem_generators": [
                "The biggest challenge in {topic} that nobody talks about is...",
                "Most professionals in {target_audience} struggle with...",
                "There's a hidden barrier in {topic} costing you opportunities..."
            ],
            "agitation_amplifiers": [
                "This isn't just a minor inconvenience—it's costing real money and time",
                "The real cost of ignoring this? Missing out on game-changing opportunities",
                "Every day you delay addressing this, the gap widens between you and your goals"
            ],
            "solution_framers": [
                "The breakthrough approach that's changing everything:",
                "Here's the counterintuitive solution that actually works:",
                "What if you could transform this challenge into your biggest advantage?"
            ]
        },
        "AIDA": {
            "description": "Attention-Interest-Desire-Action framework for engagement",
            "social_media_optimization": "Ideal for building engagement and driving action",
            "template": """
            {attention_grabber} 🔥

            {interesting_insight}

            Imagine if {desire_amplifier}...

            Ready to {call_to_action}? Drop your thoughts below! 👇
            """,
            "attention_hooks": [
                "This will completely change how you think about {topic}",
                "Plot twist: Everything you know about {topic} might be wrong",
                "The {topic} secret that industry leaders don't want you to know"
            ],
            "interest_generators": [
                "The data reveals something shocking about {topic}",
                "Most {target_audience} overlook this game-changing detail",
                "There's a hidden pattern in {topic} that successful people exploit"
            ],
            "desire_amplifiers": [
                "you could 10x your results in {topic}",
                "this strategy could transform your entire approach",
                "you could unlock opportunities you never knew existed"
            ],
            "action_prompts": [
                "join the conversation and share your experience",
                "take the first step toward transformation",
                "start implementing this strategy today"
            ]
        },
        "Before-After-Bridge": {
            "description": "Before-After-Bridge framework for transformation stories",
            "social_media_optimization": "Excellent for showcasing transformation and progress",
            "template": """
            Before: {before_situation} 😔

            After: {after_transformation} ✨

            The bridge that made it possible? {solution_bridge}

            What transformation are you working on? Share below! 💬
            """,
            "before_scenarios": [
                "Most {target_audience} were struggling with outdated approaches to {topic}",
                "Traditional methods in {topic} were failing to deliver results",
                "Professionals felt stuck using ineffective strategies for {topic}"
            ],
            "after_transformations": [
                "Teams are now achieving breakthrough results with innovative {topic} strategies",
                "Success rates have increased dramatically with the new approach to {topic}",
                "Professionals are unlocking potential they never knew they had in {topic}"
            ],
            "solution_bridges": [
                "A simple but powerful mindset shift that changed everything",
                "An innovative framework that bridges the gap between old and new",
                "A strategic approach that transforms challenges into opportunities"
            ]
        },
        "STAR": {
            "description": "Situation-Task-Action-Result framework for storytelling",
            "social_media_optimization": "Perfect for case studies and success stories",
            "template": """
            Situation: {situation_context} 📍

            Task: {task_challenge} 🎯

            Action: {action_taken} ⚡

            Result: {result_achieved} 🏆

            What's your biggest win story? Let's celebrate together! 🎉
            """,
            "situation_contexts": [
                "When {target_audience} face the challenge of {topic}",
                "In today's competitive landscape of {topic}",
                "During a critical moment in {topic} implementation"
            ],
            "task_challenges": [
                "The goal was to revolutionize our approach to {topic}",
                "We needed to solve a complex {topic} problem",
                "The challenge was to achieve breakthrough results in {topic}"
            ],
            "action_takens": [
                "We implemented an innovative strategy that focused on {topic}",
                "The team adopted a data-driven approach to {topic}",
                "We leveraged cutting-edge techniques in {topic}"
            ],
            "result_achieveds": [
                "Exceeded expectations with measurable improvements in {topic}",
                "Achieved breakthrough results that transformed our {topic} approach",
                "Delivered exceptional outcomes that set new standards in {topic}"
            ]
        },
        "Features-Advantages-Benefits": {
            "description": "Features-Advantages-Benefits framework for value proposition",
            "social_media_optimization": "Great for explaining value and benefits clearly",
            "template": """
            Feature: {feature_description} 🔧

            Advantage: {advantage_explanation} ⚡

            Benefit: {benefit_impact} 💎

            Which benefit resonates most with your goals? Comment below! 👇
            """,
            "feature_descriptions": [
                "The latest innovation in {topic} includes advanced capabilities",
                "This {topic} solution offers comprehensive functionality",
                "The new approach to {topic} features integrated systems"
            ],
            "advantage_explanations": [
                "This means faster, more efficient results in {topic}",
                "Unlike traditional methods, this approach streamlines {topic} processes",
                "The advantage is seamless integration with existing {topic} workflows"
            ],
            "benefit_impacts": [
                "You save time, reduce costs, and achieve better outcomes in {topic}",
                "Your team becomes more productive and successful with {topic}",
                "You gain competitive advantage and accelerated growth in {topic}"
            ]
        },
        "4Cs": {
            "template": """
            {clear_insight}
            
            Let's cut to the chase: {concise_point}
            
            Why does this matter? {compelling_reason}
            
            And you can trust me on this: {credibility_marker}
            """,
            "clear_insights": [
                "Here's a straightforward take on {topic}",
                "Breaking down the complexity of {topic}",
                "Simplifying the professional landscape of {topic}"
            ],
            "concise_points": [
                "success is about strategic simplicity",
                "the real value lies in focused execution",
                "professional growth happens through targeted efforts"
            ],
            "compelling_reasons": [
                "this approach could redefine your career trajectory",
                "the impact goes beyond just immediate results",
                "it's about long-term professional transformation"
            ],
            "credibility_markers": [
                "based on years of professional experience",
                "backed by real-world success stories",
                "proven across multiple industries"
            ]
        },
        "FAB": {
            "template": """
            Key feature of {topic}: {feature_highlight}
            
            Here's why it matters: {advantage_explanation}
            
            The bottom line for you: {direct_benefit}
            """,
            "feature_highlights": [
                "a groundbreaking approach that challenges the status quo",
                "an innovative strategy that most overlook",
                "a hidden gem in professional development"
            ],
            "advantage_explanations": [
                "this isn't just another run-of-the-mill solution",
                "it fundamentally changes how we think about {topic}",
                "the ripple effect is far more significant than you'd imagine"
            ],
            "direct_benefits": [
                "your professional growth accelerates",
                "you gain a competitive edge",
                "success becomes more accessible and tangible"
            ]
        }
    }

    # Select Tone and Framework Data
    tone_data = TONE_INTELLIGENCE.get(tone, TONE_INTELLIGENCE.get('Casual', {}))
    framework_data = FRAMEWORKS.get(framework.upper(), FRAMEWORKS['PAS'])

    # Intelligent Prompt Generation
    prompt = f"""
        LINKEDIN CONTENT GENERATION DIRECTIVE:

            CORE OBJECTIVES:
            1. Create a deeply human, authentic narrative about {topic}.
            2. Speak directly to {target_audience} with {tone} emotional intelligence.
            3. Utilize {framework} framework for maximum engagement.

            NARRATIVE CONSTRUCTION:
            - Storytelling Approach: {tone_data.get('narrative_styles', ['Share a personal story'])[0]}
            - Emotional Depth: {tone_data.get('emotional_depth', 'Create genuine connection')}
            - Avoid Repetition: Ensure hashtags are unique, relevant, and non-repetitive.

            VOICE:
            - First-person perspective.
            - Professional yet conversational.
            - Authentic & vulnerable.
            - Like talking to a trusted colleague.

            FORMAT STRUCTURE:
            1. Opening Hook (1-2 lines): Start with a personal insight or thought-provoking statement.

            2. Main Point/Story (3-4 short paragraphs):
            - Keep paragraphs short (2-3 lines max).
            - Use F-shaped layout for easy scanning (left-aligned text, key points stand out).

            3. Key Elements (if needed):
            - Use max 3-4 bullet points or numbers (only if necessary).
            - Avoid overloading with bullets.

            4. Closing Thought/Call to Action: End with a reflective question or invitation for discussion.

            5. Hashtags (2-3): Use unique, relevant hashtags. Avoid repetition or overused hashtags.

            TECHNICAL SPECS:
            - Word Count: 350-400 words.
            - Emojis: 1-2 professional emojis (used sparingly).
            - Paragraphs: Short and scannable (2-3 lines max).
            - Spacing: Double space between sections for clean readability.
            - F-Shaped Layout: Ensure the post is visually scannable.
            - Hashtags: Place 2-3 unique, relevant hashtags on the final line.

            CONTENT MUST:
            - Begin with a personal insight (avoid generic openings like "Key Insights").
            - Use complete, natural sentences.
            - Flow conversationally.
            - Include specific examples/experiences.
            - End with reflection or invitation for discussion.

            AVOID:
            - Corporate jargon.
            - Generic motivation.
            - Buzzwords.
            - Exaggerated claims.
            - Bullet-heavy content.
            - Directive-style openings.
            - Repetitive or irrelevant hashtags.

            TONE BALANCE:
            - Professional > Polished.
            - Authentic > Perfect.
            - Personal > Corporate.

            FRAMEWORK SPECIFIC GUIDANCE:
            {json.dumps(framework_data, indent=2)}

            FINAL REQUIREMENT:
            Craft a LinkedIn post that feels like a genuine, {tone} conversation about {topic}, structured through the {framework} lens, 
            speaking authentically to {target_audience}'s professional experiences and aspirations. Ensure:
            - No repetitive hashtags.
            - Proper spacing and F-shaped structure.
            - Clear, scannable, and engaging content.
            - Use bullet points or numbers only where necessary.
    """
    return prompt


def create_long_content_rewrite_prompt(original_content, target_audience, tone, framework):
    """
    Create a specialized prompt for long content refinement and optimization with framework integration.

    :param original_content: The content to be rewritten and optimized
    :param target_audience: Specific professional or demographic group
    :param tone: Emotional and communication style
    :param framework: Content framework to apply (AIDA, PAS, etc.)
    """

    # Framework-specific optimization strategies
    framework_strategies = {
        "AIDA": {
            "structure": "Attention → Interest → Desire → Action",
            "optimization": "Enhance attention-grabbing opening, build compelling interest, create strong desire, and include clear call-to-action"
        },
        "PAS": {
            "structure": "Problem → Agitation → Solution",
            "optimization": "Strengthen problem identification, intensify pain points, and present compelling solution"
        },
        "Before-After-Bridge": {
            "structure": "Current State → Desired State → Bridge/Solution",
            "optimization": "Clarify current challenges, paint vivid picture of desired outcome, provide clear path forward"
        },
        "STAR": {
            "structure": "Situation → Task → Action → Result",
            "optimization": "Set clear context, define specific objectives, detail actions taken, highlight measurable results"
        },
        "Features-Advantages-Benefits": {
            "structure": "What it is → Why it matters → How it helps",
            "optimization": "Clearly explain features, connect to advantages, emphasize personal benefits"
        },
        "None": {
            "structure": "Natural flow without specific framework",
            "optimization": "Enhance natural storytelling, improve engagement, strengthen emotional connection"
        }
    }

    framework_info = framework_strategies.get(framework, framework_strategies["None"])

    prompt = f"""
    You are an expert long-form content optimizer who specializes in refining and enhancing social media content to maximize engagement, authenticity, and framework effectiveness. Your goal is to transform the provided content into a more compelling, human-like post that follows the {framework} framework structure.

    CONTENT REFINEMENT APPROACH:
    - Enhance clarity and readability while maintaining the core message
    - Optimize for maximum engagement and shareability
    - Ensure the content sounds authentically human-written, not AI-generated
    - Improve emotional connection with the target audience
    - Strengthen framework-specific elements and flow
    - Maintain the original intent while making it more impactful

    TARGET AUDIENCE: {target_audience}
    TONE: {tone}
    FRAMEWORK: {framework}

    FRAMEWORK OPTIMIZATION:
    - Structure: {framework_info['structure']}
    - Strategy: {framework_info['optimization']}

    LONG-FORM CONTENT REQUIREMENTS:
    - Length: 200-400 words for comprehensive coverage
    - Structure: Clear sections that follow the {framework} framework
    - Engagement: Multiple engagement hooks throughout the content
    - Storytelling: Rich narrative elements and specific examples
    - Professional depth: Industry insights and expert perspective

    OPTIMIZATION REQUIREMENTS:
    - Make the opening hook more scroll-stopping and attention-grabbing
    - Enhance storytelling elements with specific, relatable details
    - Improve the flow and readability of the content
    - Strengthen emotional triggers and psychological engagement
    - Optimize hashtag selection for better discoverability
    - Ensure platform-appropriate length and formatting
    - Add authentic human touches (contractions, natural speech patterns)

    HUMAN-LIKE AUTHENTICITY MARKERS:
    - Use conversational language with natural imperfections
    - Include genuine emotional expressions and reactions
    - Add personal insights or "aha moments"
    - Use specific examples instead of generic statements
    - Show vulnerability or relatability where appropriate
    - Incorporate natural speech patterns and casual contractions

    CRITICAL OUTPUT REQUIREMENTS:
    🚨 NEVER include any prefacing text like:
    - "Here's a refined version..."
    - "Here's the optimized content..."
    - "Here's the rewritten post..."
    - Any introductory explanations or meta-commentary

    🚨 NEVER include formatting separators like:
    - "---" dividers
    - "***" separators
    - Any formatting instructions or dividers

    🚨 START IMMEDIATELY with the enhanced hook/opening line
    - Begin directly with your improved scroll-stopping first sentence
    - No preamble, no setup, no introduction
    - Jump straight into the optimized content

    🚨 OUTPUT ONLY the refined social media post content that users can immediately copy and paste to their platforms

    ORIGINAL CONTENT TO REFINE AND OPTIMIZE:
    {original_content}

    Transform this content into a more engaging, authentic, and impactful long-form social media post that follows the {framework} framework and drives meaningful audience interaction.
    """

    return prompt




def long_content_generation(topic, tone, target_audience, framework, model_name = None, long_content = None, rewrite_content = None):
    """
    Generate a long content using the advanced content generation framework.

    :param topic: Core subject of the content
    :param target_audience: Specific professional or demographic group
    :param tone: Emotional and communication style
    :param framework: Content framework to use (AIDA, PAS, etc.)
    :param model_name: Optional model name for content generation
    :param long_content: Optional parameter for longer content generation with identical functionality to topic
    :param rewrite_content: Optional parameter for content refinement and optimization
    """

    # Default model (Anthropic)
    default_model = os.getenv("default_model")

    # Determine content input based on parameter priority: rewrite_content > long_content > topic
    if rewrite_content is not None:
        content_input = rewrite_content
        # For rewrite content, use a specialized prompt that focuses on refinement
        prompt = create_long_content_rewrite_prompt(content_input, target_audience, tone, framework)
    elif long_content is not None:
        content_input = long_content
        # Use long_content with the specified framework
        if framework == "None":
            prompt = advanced_content_generator_default(content_input, target_audience, tone)
        else:
            prompt = advanced_content_generator_with_framework(content_input, target_audience, tone, framework)
    else:
        content_input = topic
        # Use topic with the specified framework
        if framework == "None":
            prompt = advanced_content_generator_default(content_input, target_audience, tone)
        else:
            prompt = advanced_content_generator_with_framework(content_input, target_audience, tone, framework)

    response = model_call_for_long_content_feature(prompt, model_name)

    # response = clean_and_complete_string(response)

    response_list = [response]
    # Convert list to JSON format
    json_data = [{"text": text} for text in response_list]

    # Output JSON as a string
    json_output = json.dumps(json_data, ensure_ascii=False, indent=4)

    # Parse the raw JSON string
    parsed_result = json.loads(json_output)

    # Extract and clean the text content
    cleaned_texts = [{"text": item["text"]} for item in parsed_result]

    return cleaned_texts