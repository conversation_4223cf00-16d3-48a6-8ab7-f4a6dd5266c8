# Content Summarizer API Documentation

## Overview

The Content Summarizer module generates concise, accurate summaries of articles from URLs with customizable length specifications and authentic human-written style. It features enhanced content extraction with retry logic, multiple extraction strategies, and robust error handling for various website structures.

## Features

- **Enhanced URL Content Extraction**: Multi-strategy extraction with retry logic and exponential backoff
- **Customizable Summary Lengths**: Short (50-100 words), Medium (100-200 words), Long (200-300 words)
- **Platform Optimization**: Tailored content for LinkedIn, Twitter, Facebook, or general use
- **Authentic Human Voice**: Bypasses AI detection with natural, engaging writing
- **Robust Error Handling**: Graceful handling of network issues, paywalls, and JavaScript-heavy sites
- **Clean Output**: Ready-to-post content without prefacing text or formatting

## API Endpoint

### POST /process

Generate summaries from article URLs with customizable parameters.

**Request Body:**
```json
{
    "data": {
        "url": "https://example.com/article",
        "summary_length": "medium",
        "tone": "neutral",
        "platform": "linkedin",
        "model_name": "azure/gpt-4o"
    },
    "operation": "create_summary"
}
```

## Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `url` | string | The post/article URL to summarize |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `summary_length` | string | "medium" | Length specification: "short" (50-100 words), "medium" (100-200 words), "long" (200-300 words) |
| `tone` | string | "neutral" | Tone for the summary (e.g., "neutral", "professional", "informative") |
| `platform` | string | "general" | Target platform optimization ("linkedin", "twitter", "facebook", "general") |
| `model_name` | string | null | Azure OpenAI model to use for generation |

## Summary Length Specifications

### Short Summary (50-100 words)
- **Focus**: Core message and key takeaway
- **Use Case**: Quick overviews, social media posts, brief updates
- **Content**: Concise overview capturing only the most essential points

### Medium Summary (100-200 words)
- **Focus**: Main arguments, key data points, and primary conclusions
- **Use Case**: Professional sharing, newsletter content, balanced coverage
- **Content**: Balanced summary covering main points and supporting details

### Long Summary (200-300 words)
- **Focus**: Complete overview including context, arguments, evidence, and implications
- **Use Case**: Detailed analysis, comprehensive reports, thorough coverage
- **Content**: Comprehensive summary with detailed coverage of all major points

## Platform Optimizations

### LinkedIn
- Professional tone suitable for business networking
- Thought leadership sharing format
- Industry insights focus

### Twitter
- Engaging and shareable format
- Optimized for social media consumption
- Concise and impactful presentation

### Facebook
- Accessible and discussion-friendly tone
- Community engagement focus
- Relatable and conversational style

### General
- Versatile format suitable for multiple platforms
- Balanced approach to tone and presentation

## Enhanced Content Extraction

### Multi-Strategy Extraction
1. **Primary Strategy**: Common article content containers (article, main, .content, etc.)
2. **Secondary Strategy**: Paragraph-based extraction within content areas
3. **Fallback Strategy**: Body text with paragraph filtering
4. **Last Resort**: Full body text extraction

### Retry Logic
- **Maximum Retries**: 3 attempts with exponential backoff
- **Timeout Handling**: Configurable timeout with graceful degradation
- **Header Rotation**: Different browser headers for each retry attempt
- **Error Recovery**: Detailed logging and meaningful error messages

### Supported Content Types
- News articles and blog posts
- Academic papers and research articles
- Business reports and whitepapers
- Opinion pieces and editorial content
- Technical documentation and guides

## Response Format

### Success Response (200)
```json
{
    "result": [
        {
            "text": "Generated summary content ready for use..."
        }
    ],
    "status": "success",
    "message": "Operation 'create_summary' completed successfully"
}
```

### Error Response (400/500)
```json
{
    "detail": "Error description",
    "status_code": 400
}
```

## Usage Examples

### Basic Summary Generation
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://finshots.in/archive/economics-of-the-worlds-largest-spanish-christmas-lottery-el-gordo-nirmala-sitharaman-popcorn-gst/",
      "summary_length": "medium",
      "tone": "neutral",
      "platform": "linkedin"
    },
    "operation": "create_summary"
  }'
```

### Short Summary for Social Media
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://example.com/article",
      "summary_length": "short",
      "tone": "engaging",
      "platform": "twitter",
      "model_name": "azure/grok-3"
    },
    "operation": "create_summary"
  }'
```

### Python Client Example
```python
import requests

def create_summary(url, summary_length="medium", tone="neutral", platform="general"):
    payload = {
        "data": {
            "url": url,
            "summary_length": summary_length,
            "tone": tone,
            "platform": platform
        },
        "operation": "create_summary"
    }
    
    response = requests.post(
        "http://localhost:8000/process",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        return result["result"][0]["text"]
    else:
        raise Exception(f"API call failed: {response.text}")

# Usage examples
short_summary = create_summary(
    url="https://example.com/article",
    summary_length="short",
    tone="neutral",
    platform="twitter"
)

long_summary = create_summary(
    url="https://example.com/article",
    summary_length="long",
    tone="professional",
    platform="linkedin"
)
```

## Error Handling

### Common Errors

| Error | Description | Solution |
|-------|-------------|----------|
| Missing required field: 'url' | URL parameter not provided | Include valid URL in request |
| Invalid URL format | Malformed URL provided | Provide valid HTTP/HTTPS URL |
| Invalid summary_length | Length not in allowed values | Use "short", "medium", or "long" |
| URL fetching failed | Network or server error | Check URL accessibility and network connection |
| Insufficient content extracted | Article behind paywall or minimal content | Try different URL or check content accessibility |
| Request timeout | Server took too long to respond | Retry request or check server status |

### Enhanced Error Recovery

The Content Summarizer includes sophisticated error handling:

- **Network Issues**: Automatic retry with exponential backoff
- **Timeout Handling**: Configurable timeouts with graceful degradation
- **Content Extraction Failures**: Multiple extraction strategies with fallbacks
- **Paywall Detection**: Clear error messages when content is inaccessible
- **JavaScript-Heavy Sites**: Attempts multiple extraction methods

### Best Practices

1. **URL Validation**: Ensure URLs are accessible and contain substantial text content
2. **Length Selection**: Choose appropriate summary length for your use case
3. **Error Handling**: Implement proper error handling for network and parsing failures
4. **Rate Limiting**: Be mindful of API rate limits when making multiple requests
5. **Content Review**: Review generated summaries for accuracy and completeness

## Model Compatibility

### Recommended Models

| Use Case | Recommended Model | Reason |
|----------|------------------|---------|
| General Summaries | azure/gpt-4o | Balanced performance and accuracy |
| Creative Summaries | azure/grok-3 | Enhanced engagement and readability |
| Technical Content | azure/gpt-4.1 | Superior reasoning for complex topics |
| Budget-Friendly | azure/gpt-4o-mini | Cost-effective for simple summaries |

### Model Parameters
- **Temperature**: 0.7 (balanced for accuracy and natural language)
- **Max Tokens**: 800 (sufficient for up to 300-word summaries)
- **System Prompt**: Optimized for authentic, human-written summaries

## Integration with Existing Systems

The Content Summarizer seamlessly integrates with the existing content generation ecosystem:

- **Consistent API Pattern**: Follows the same request/response format as other modules
- **Model Manager Integration**: Uses the unified model management system
- **Helicone Logging**: Automatic API monitoring and analytics
- **MongoDB Storage**: Optional storage following existing patterns
- **Clean Output Standards**: Implements the same post-processing as other modules

## Testing

Use the provided test script and Postman collection:

```bash
# Run module tests
python test_content_summarizer.py

# Import Postman collection
# File: postman/content_summarizer_collection.json
```

## Version Information
- **Implementation Version**: 1.0.0
- **Clean Output Standard**: Following CLEAN_OUTPUT_IMPLEMENTATION.md
- **Azure OpenAI Compatibility**: All configured deployments
- **LiteLLM Integration**: Unified interface for all models
- **Enhanced Error Handling**: Multi-retry logic with exponential backoff
