#!/usr/bin/env python3
"""
Basic usage examples for the Enhanced FastAPI application with Azure OpenAI integration.
Demonstrates common use cases and best practices.
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class GrowerAIClient:
    """Simple client for interacting with the Enhanced FastAPI application."""
    
    def __init__(self, base_url: str = "http://localhost:8000", helicone_api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # Setup Helicone headers if API key provided
        if helicone_api_key:
            self.session.headers.update({
                "Helicone-Auth": f"Bearer {helicone_api_key}",
                "Helicone-Cache-Enabled": "true",
                "Helicone-Property-Client": "python-example"
            })
    
    def generate_content(self, prompt: str, model: str = "azure/gpt-4o", **kwargs) -> Dict[str, Any]:
        """Generate content using the specified model."""
        payload = {
            "data": prompt,
            "operation": "content_generation",
            "parameters": {
                "model": model,
                "max_tokens": kwargs.get("max_tokens", 1000),
                "temperature": kwargs.get("temperature", 0.7),
                "use_case": kwargs.get("use_case", "general")
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "Helicone-Property-Use-Case": kwargs.get("use_case", "general")
        }
        
        response = self.session.post(f"{self.base_url}/process", json=payload, headers=headers)
        response.raise_for_status()
        return response.json()
    
    def rewrite_content(self, content: str, style: str = "professional", **kwargs) -> Dict[str, Any]:
        """Rewrite content with specified style."""
        payload = {
            "content": content,
            "style": style,
            "tone": kwargs.get("tone", "engaging"),
            "target_audience": kwargs.get("target_audience", "business_professionals"),
            "preserve_key_points": True,
            "model": kwargs.get("model", "azure/gpt-4o")
        }
        
        response = self.session.post(f"{self.base_url}/rewrite-ai-post", json=payload)
        response.raise_for_status()
        return response.json()
    
    def get_model_recommendation(self, use_case: str) -> Dict[str, Any]:
        """Get recommended model for a specific use case."""
        response = self.session.get(f"{self.base_url}/models/recommended/{use_case}")
        response.raise_for_status()
        return response.json()

def example_content_generation():
    """Example: Generate different types of content."""
    print("=== Content Generation Examples ===")
    
    client = GrowerAIClient()
    
    # Example 1: Marketing content
    print("\n1. Marketing Content Generation:")
    result = client.generate_content(
        prompt="Create a compelling product announcement for a new AI-powered project management tool",
        model="azure/grok-3",
        use_case="creative_writing",
        temperature=0.8,
        max_tokens=500
    )
    print(f"Generated content: {result['result'][:200]}...")
    print(f"Model used: {result['metadata']['model_used']}")
    print(f"Response time: {result['metadata']['response_time']:.2f}s")
    
    # Example 2: Technical documentation
    print("\n2. Technical Documentation:")
    result = client.generate_content(
        prompt="Explain the benefits of microservices architecture for enterprise applications",
        model="azure/deepseek",
        use_case="technical_analysis",
        temperature=0.3,
        max_tokens=800
    )
    print(f"Generated content: {result['result'][:200]}...")
    print(f"Model used: {result['metadata']['model_used']}")
    
    # Example 3: Creative writing
    print("\n3. Creative Writing:")
    result = client.generate_content(
        prompt="Write an engaging story about a developer who discovers an AI that can predict software bugs",
        model="azure/grok-3",
        use_case="creative_writing",
        temperature=0.9,
        max_tokens=600
    )
    print(f"Generated content: {result['result'][:200]}...")

def example_content_rewriting():
    """Example: Rewrite content for different audiences."""
    print("\n=== Content Rewriting Examples ===")
    
    client = GrowerAIClient()
    
    original_content = """
    Our new software update has some cool features. It's faster and has better security. 
    Users will like the new interface and it's easier to use now.
    """
    
    # Example 1: Professional rewrite
    print("\n1. Professional Style:")
    result = client.rewrite_content(
        content=original_content,
        style="professional",
        tone="authoritative",
        target_audience="enterprise_executives"
    )
    print(f"Original: {original_content.strip()}")
    print(f"Rewritten: {result['rewritten_content']}")
    print(f"Improvements: {', '.join(result['improvements'])}")
    
    # Example 2: Casual rewrite
    print("\n2. Casual Style:")
    result = client.rewrite_content(
        content=original_content,
        style="casual",
        tone="friendly",
        target_audience="general_users"
    )
    print(f"Rewritten: {result['rewritten_content']}")
    
    # Example 3: Technical rewrite
    print("\n3. Technical Style:")
    result = client.rewrite_content(
        content=original_content,
        style="technical",
        tone="informative",
        target_audience="developers"
    )
    print(f"Rewritten: {result['rewritten_content']}")

def example_model_selection():
    """Example: Get model recommendations for different use cases."""
    print("\n=== Model Selection Examples ===")
    
    client = GrowerAIClient()
    
    use_cases = [
        "creative_writing",
        "technical_analysis", 
        "content_rewriting",
        "simple_tasks",
        "code_generation"
    ]
    
    for use_case in use_cases:
        try:
            recommendation = client.get_model_recommendation(use_case)
            print(f"\nUse case: {use_case}")
            print(f"Recommended model: {recommendation['recommended_model']}")
            print(f"Reasoning: {recommendation['model_info'].get('reasoning', 'N/A')}")
        except Exception as e:
            print(f"Error getting recommendation for {use_case}: {e}")

def example_error_handling():
    """Example: Proper error handling and retries."""
    print("\n=== Error Handling Examples ===")
    
    client = GrowerAIClient()
    
    def robust_api_call(prompt, max_retries=3):
        """Make API call with retry logic."""
        for attempt in range(max_retries):
            try:
                result = client.generate_content(prompt)
                return result
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt
                    print(f"Rate limited. Waiting {wait_time}s before retry...")
                    time.sleep(wait_time)
                    continue
                elif e.response.status_code >= 500:  # Server error
                    print(f"Server error: {e.response.status_code}. Retrying...")
                    continue
                else:
                    raise  # Client error, don't retry
            except requests.exceptions.RequestException as e:
                print(f"Request failed: {e}")
                if attempt == max_retries - 1:
                    raise
        
        raise Exception(f"Failed after {max_retries} attempts")
    
    # Test with a simple prompt
    try:
        result = robust_api_call("Write a brief introduction to machine learning")
        print(f"Success: {result['result'][:100]}...")
    except Exception as e:
        print(f"All retries failed: {e}")

def example_batch_processing():
    """Example: Process multiple requests efficiently."""
    print("\n=== Batch Processing Example ===")
    
    client = GrowerAIClient()
    
    prompts = [
        "Explain artificial intelligence in one paragraph",
        "Describe the benefits of cloud computing",
        "What is machine learning?",
        "Define data science briefly",
        "Explain blockchain technology"
    ]
    
    print(f"Processing {len(prompts)} prompts...")
    start_time = time.time()
    
    results = []
    for i, prompt in enumerate(prompts):
        try:
            result = client.generate_content(
                prompt=prompt,
                model="azure/gpt-35-turbo",  # Fast model for batch processing
                max_tokens=200,
                temperature=0.5
            )
            results.append(result)
            print(f"Completed {i+1}/{len(prompts)}")
            
            # Small delay to avoid rate limits
            time.sleep(0.1)
            
        except Exception as e:
            print(f"Failed prompt {i+1}: {e}")
            results.append(None)
    
    total_time = time.time() - start_time
    successful_results = [r for r in results if r is not None]
    
    print(f"\nBatch Processing Summary:")
    print(f"Total time: {total_time:.2f}s")
    print(f"Successful requests: {len(successful_results)}/{len(prompts)}")
    print(f"Average time per request: {total_time/len(prompts):.2f}s")
    
    # Show sample results
    for i, result in enumerate(successful_results[:3]):
        if result:
            print(f"\nSample result {i+1}: {result['result'][:100]}...")

def main():
    """Run all examples."""
    print("Enhanced FastAPI - Python Usage Examples")
    print("=" * 50)
    
    try:
        # Test basic connectivity first
        client = GrowerAIClient()
        health_response = client.session.get(f"{client.base_url}/health")
        health_response.raise_for_status()
        print("✅ API is healthy and accessible")
        
        # Run examples
        example_content_generation()
        example_content_rewriting()
        example_model_selection()
        example_error_handling()
        example_batch_processing()
        
        print("\n" + "=" * 50)
        print("✅ All examples completed successfully!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error running examples: {e}")

if __name__ == "__main__":
    main()
