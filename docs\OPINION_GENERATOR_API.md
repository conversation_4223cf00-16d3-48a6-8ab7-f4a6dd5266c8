# Opinion Generator API Documentation

## Overview

The Opinion Generator module creates authentic, human-written social media posts that express genuine opinions about articles from provided URLs. It analyzes article content and generates either supportive or opposing viewpoints while staying strictly grounded in the source material.

## Features

- **URL Content Extraction**: Robust web scraping with BeautifulSoup4 for article parsing
- **Stance-Based Generation**: Support for both "agree" and "disagree" positions
- **Platform Optimization**: Tailored content for LinkedIn, Twitter, Facebook, or general use
- **Authentic Human Voice**: Bypasses AI detection with natural, conversational writing
- **Grounded Analysis**: Strictly based on source article content without hallucination
- **Clean Output**: Ready-to-post content without prefacing text or formatting

## API Endpoint

### POST /process

Generate opinion-based social media content from article URLs.

**Request Body:**
```json
{
    "data": {
        "url": "https://example.com/article",
        "stance": "agree",
        "tone": "professional",
        "platform": "linkedin",
        "model_name": "azure/gpt-4o"
    },
    "operation": "opinion_generator"
}
```

## Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `url` | string | The article URL to analyze and form an opinion about |
| `stance` | string | Either "agree" or "disagree" - determines support or opposition stance |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `tone` | string | "professional" | Tone of voice for the opinion (e.g., "professional", "casual", "passionate") |
| `platform` | string | "general" | Target platform optimization ("linkedin", "twitter", "facebook", "general") |
| `model_name` | string | null | Azure OpenAI model to use for generation |

## Platform Optimizations

### LinkedIn
- Professional networking context
- Thought leadership tone
- Industry insights focus
- Longer-form content suitable for professional discussions

### Twitter
- Concise and engaging
- Conversation-starting elements
- Hashtag-friendly format
- Punchy, shareable content

### Facebook
- Community-oriented approach
- Discussion-friendly tone
- Personal storytelling elements
- Relatable and engaging

### General
- Versatile format suitable for multiple platforms
- Balanced approach to tone and length

## Response Format

### Success Response (200)
```json
{
    "result": [
        {
            "text": "Generated opinion post content ready for social media..."
        }
    ],
    "status": "success",
    "message": "Operation 'opinion_generator' completed successfully"
}
```

### Error Response (400/500)
```json
{
    "detail": "Error description",
    "status_code": 400
}
```

## Content Quality Guidelines

### Authenticity Features
- Natural, conversational language with varied sentence structures
- Avoids corporate speak and AI-typical phrases
- Includes rhetorical questions and personal observations
- Uses contractions and informal language where appropriate
- Shows genuine emotion and conviction

### Content Requirements
- **Length**: 200-400 words for substantial commentary
- **Grounded Analysis**: Based strictly on article information
- **Specific References**: Quotes or references specific points from the article
- **Engaging Hook**: Attention-grabbing opening
- **Conversation Starter**: Elements that encourage discussion

## Usage Examples

### Basic Opinion Generation
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://finshots.in/archive/economics-of-the-worlds-largest-spanish-christmas-lottery-el-gordo-nirmala-sitharaman-popcorn-gst/",
      "stance": "agree",
      "tone": "professional",
      "platform": "linkedin"
    },
    "operation": "opinion_generator"
  }'
```

### Disagree Stance with Casual Tone
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://example.com/article",
      "stance": "disagree",
      "tone": "casual",
      "platform": "twitter",
      "model_name": "azure/grok-3"
    },
    "operation": "opinion_generator"
  }'
```

### Python Client Example
```python
import requests

def generate_opinion(url, stance, tone="professional", platform="general"):
    payload = {
        "data": {
            "url": url,
            "stance": stance,
            "tone": tone,
            "platform": platform
        },
        "operation": "opinion_generator"
    }
    
    response = requests.post(
        "http://localhost:8000/process",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        return result["result"][0]["text"]
    else:
        raise Exception(f"API call failed: {response.text}")

# Usage
opinion_post = generate_opinion(
    url="https://example.com/article",
    stance="agree",
    tone="passionate",
    platform="facebook"
)
print(opinion_post)
```

## Error Handling

### Common Errors

| Error | Description | Solution |
|-------|-------------|----------|
| Missing required field: 'url' | URL parameter not provided | Include valid URL in request |
| Missing required field: 'stance' | Stance parameter not provided | Include "agree" or "disagree" stance |
| Invalid stance | Stance not "agree" or "disagree" | Use valid stance values |
| Invalid URL format | Malformed URL provided | Provide valid HTTP/HTTPS URL |
| URL fetching failed | Network or server error | Check URL accessibility |
| Insufficient content extracted | Article behind paywall or JS-heavy | Try different URL or check accessibility |

### Best Practices

1. **URL Validation**: Ensure URLs are accessible and contain substantial text content
2. **Error Handling**: Implement proper error handling for network and parsing failures
3. **Rate Limiting**: Be mindful of API rate limits when making multiple requests
4. **Content Review**: Review generated content for appropriateness before posting
5. **Platform Guidelines**: Ensure generated content complies with target platform policies

## Model Compatibility

### Recommended Models

| Use Case | Recommended Model | Reason |
|----------|------------------|---------|
| Professional Content | azure/gpt-4o | Balanced performance and quality |
| Creative Opinions | azure/grok-3 | Enhanced creativity and engagement |
| Technical Analysis | azure/gpt-4.1 | Superior reasoning capabilities |
| Budget-Friendly | azure/gpt-4o-mini | Cost-effective for simple opinions |

### Model Parameters
- **Temperature**: 0.86 (optimized for authentic, varied content)
- **Max Tokens**: 1200 (sufficient for 200-400 word posts)
- **Top P**: Automatically optimized by model manager

## Integration with Existing Systems

The Opinion Generator seamlessly integrates with the existing content generation ecosystem:

- **Consistent API Pattern**: Follows the same request/response format as other modules
- **Model Manager Integration**: Uses the unified model management system
- **Helicone Logging**: Automatic API monitoring and analytics
- **MongoDB Storage**: Optional storage following existing patterns
- **Clean Output Standards**: Implements the same post-processing as other modules

## Testing

Use the provided test script and Postman collection:

```bash
# Run module tests
python test_opinion_generator.py

# Import Postman collection
# File: postman/opinion_generator_collection.json
```

## Version Information
- **Implementation Version**: 1.0.0
- **Clean Output Standard**: Following CLEAN_OUTPUT_IMPLEMENTATION.md
- **Azure OpenAI Compatibility**: All configured deployments
- **LiteLLM Integration**: Unified interface for all models
