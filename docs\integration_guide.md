# Developer Integration Guide

Step-by-step guide for integrating with the Enhanced FastAPI application, including code samples in Python, JavaScript, and cURL for all major use cases.

## 🚀 Quick Start

### Prerequisites
- API endpoint access (default: `http://localhost:8000`)
- Optional: Helicone API key for monitoring
- HTTP client library for your preferred language

### Base Configuration
```bash
# Environment variables
API_BASE_URL=http://localhost:8000
HELICONE_API_KEY=your_helicone_api_key_here  # Optional
```

## 🐍 Python Integration

### Installation
```bash
pip install requests httpx  # Choose your preferred HTTP client
```

### Basic Client Setup
```python
import requests
import json
from typing import Dict, Any, Optional

class GrowerAIClient:
    def __init__(self, base_url: str = "http://localhost:8000", helicone_api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # Setup Helicone headers if API key provided
        if helicone_api_key:
            self.session.headers.update({
                "Helicone-Auth": f"Bearer {helicone_api_key}",
                "Helicone-Cache-Enabled": "true",
                "Helicone-Property-Client": "python-sdk"
            })
    
    def generate_content(self, 
                        prompt: str, 
                        model: str = "azure/gpt-4o",
                        use_case: str = "general",
                        max_tokens: int = 1000,
                        temperature: float = 0.7) -> Dict[str, Any]:
        """Generate content using the specified model."""
        
        payload = {
            "data": prompt,
            "operation": "content_generation",
            "parameters": {
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "use_case": use_case
            }
        }
        
        # Add use case specific headers
        headers = {
            "Content-Type": "application/json",
            "Helicone-Property-Use-Case": use_case
        }
        
        response = self.session.post(
            f"{self.base_url}/process",
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    
    def rewrite_content(self,
                       content: str,
                       style: str = "professional",
                       tone: str = "engaging",
                       model: str = "azure/gpt-4o") -> Dict[str, Any]:
        """Rewrite content with specified style and tone."""
        
        payload = {
            "content": content,
            "style": style,
            "tone": tone,
            "target_audience": "business_professionals",
            "preserve_key_points": True,
            "model": model
        }
        
        headers = {
            "Content-Type": "application/json",
            "Helicone-Property-Use-Case": "content_rewriting"
        }
        
        response = self.session.post(
            f"{self.base_url}/rewrite-ai-post",
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        return response.json()
    
    def get_model_recommendation(self, use_case: str) -> Dict[str, Any]:
        """Get recommended model for a specific use case."""
        
        response = self.session.get(
            f"{self.base_url}/models/recommended/{use_case}"
        )
        response.raise_for_status()
        return response.json()
    
    def compare_models(self,
                      prompt: str,
                      models: list,
                      evaluation_criteria: list = None) -> Dict[str, Any]:
        """Compare performance across multiple models."""
        
        if evaluation_criteria is None:
            evaluation_criteria = ["accuracy", "clarity", "engagement"]
        
        payload = {
            "prompt": prompt,
            "models": models,
            "evaluation_criteria": evaluation_criteria,
            "include_cost_analysis": True
        }
        
        response = self.session.post(
            f"{self.base_url}/models/compare",
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def health_check(self) -> Dict[str, Any]:
        """Check API health status."""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()

# Usage Examples
if __name__ == "__main__":
    # Initialize client
    client = GrowerAIClient(
        base_url="http://localhost:8000",
        helicone_api_key="your_helicone_api_key"  # Optional
    )
    
    # Example 1: Generate marketing content
    result = client.generate_content(
        prompt="Write a compelling product description for an AI-powered fitness tracker",
        model="azure/grok-3",
        use_case="creative_writing",
        temperature=0.8
    )
    print("Generated content:", result['result'])
    
    # Example 2: Repurpose content to be authentically human-written
    repurposed = client.repurpose_content(
        content="Our app is really good and helps people track their fitness goals",
        tone="professional",
        target_audience="fitness enthusiasts"
    )
    print("Rewritten content:", rewritten['rewritten_content'])
    
    # Example 3: Get model recommendation
    recommendation = client.get_model_recommendation("technical_analysis")
    print("Recommended model:", recommendation['recommended_model'])
    
    # Example 4: Compare models
    comparison = client.compare_models(
        prompt="Explain quantum computing in simple terms",
        models=["azure/gpt-4o", "azure/gpt-4.1", "azure/deepseek"]
    )
    print("Best model:", comparison['winner'])
```

### Async Python Client
```python
import asyncio
import aiohttp
from typing import Dict, Any, Optional

class AsyncGrowerAIClient:
    def __init__(self, base_url: str = "http://localhost:8000", helicone_api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.headers = {"Content-Type": "application/json"}
        
        if helicone_api_key:
            self.headers.update({
                "Helicone-Auth": f"Bearer {helicone_api_key}",
                "Helicone-Cache-Enabled": "true"
            })
    
    async def generate_content(self, prompt: str, model: str = "azure/gpt-4o", **kwargs) -> Dict[str, Any]:
        """Async content generation."""
        payload = {
            "data": prompt,
            "operation": "content_generation",
            "parameters": {"model": model, **kwargs}
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/process",
                json=payload,
                headers=self.headers
            ) as response:
                response.raise_for_status()
                return await response.json()
    
    async def batch_generate(self, prompts: list, model: str = "azure/gpt-4o") -> list:
        """Generate content for multiple prompts concurrently."""
        tasks = [
            self.generate_content(prompt, model) 
            for prompt in prompts
        ]
        return await asyncio.gather(*tasks)

# Async usage example
async def main():
    client = AsyncGrowerAIClient()
    
    # Batch processing
    prompts = [
        "Write a blog post about AI in healthcare",
        "Create a product description for smart home devices",
        "Generate a technical whitepaper outline"
    ]
    
    results = await client.batch_generate(prompts, model="azure/gpt-4o")
    for i, result in enumerate(results):
        print(f"Result {i+1}: {result['result'][:100]}...")

# Run async example
# asyncio.run(main())
```

## 🌐 JavaScript Integration

### Node.js Setup
```bash
npm install axios node-fetch  # Choose your preferred HTTP client
```

### JavaScript Client
```javascript
const axios = require('axios');

class GrowerAIClient {
    constructor(baseUrl = 'http://localhost:8000', heliconeApiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.client = axios.create({
            baseURL: this.baseUrl,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        // Setup Helicone headers if API key provided
        if (heliconeApiKey) {
            this.client.defaults.headers['Helicone-Auth'] = `Bearer ${heliconeApiKey}`;
            this.client.defaults.headers['Helicone-Cache-Enabled'] = 'true';
            this.client.defaults.headers['Helicone-Property-Client'] = 'javascript-sdk';
        }
    }
    
    async generateContent(prompt, options = {}) {
        const {
            model = 'azure/gpt-4o',
            useCase = 'general',
            maxTokens = 1000,
            temperature = 0.7
        } = options;
        
        const payload = {
            data: prompt,
            operation: 'content_generation',
            parameters: {
                model,
                max_tokens: maxTokens,
                temperature,
                use_case: useCase
            }
        };
        
        const headers = {
            'Helicone-Property-Use-Case': useCase
        };
        
        try {
            const response = await this.client.post('/process', payload, { headers });
            return response.data;
        } catch (error) {
            throw new Error(`Content generation failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async repurposeContent(content, options = {}) {
        const {
            tone = 'professional',
            target_audience = 'general',
            model_name = 'azure/gpt-4o'
        } = options;

        const payload = {
            data: {
                content,
                tone,
                target_audience,
                model_name
            },
            operation: 'repurpose_content'
        };

        try {
            const response = await this.client.post('/process', payload);
            return response.data;
        } catch (error) {
            throw new Error(`Content repurposing failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async getModelRecommendation(useCase) {
        try {
            const response = await this.client.get(`/models/recommended/${useCase}`);
            return response.data;
        } catch (error) {
            throw new Error(`Model recommendation failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async compareModels(prompt, models, evaluationCriteria = ['accuracy', 'clarity', 'engagement']) {
        const payload = {
            prompt,
            models,
            evaluation_criteria: evaluationCriteria,
            include_cost_analysis: true
        };
        
        try {
            const response = await this.client.post('/models/compare', payload);
            return response.data;
        } catch (error) {
            throw new Error(`Model comparison failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async healthCheck() {
        try {
            const response = await this.client.get('/health');
            return response.data;
        } catch (error) {
            throw new Error(`Health check failed: ${error.response?.data?.detail || error.message}`);
        }
    }
}

// Usage Examples
async function examples() {
    const client = new GrowerAIClient(
        'http://localhost:8000',
        process.env.HELICONE_API_KEY  // Optional
    );
    
    try {
        // Example 1: Generate creative content
        const content = await client.generateContent(
            "Create an engaging social media post about sustainable technology",
            {
                model: 'azure/grok-3',
                useCase: 'creative_writing',
                temperature: 0.8
            }
        );
        console.log('Generated content:', content.result);
        
        // Example 2: Rewrite content
        const rewritten = await client.rewriteContent(
            "Our product is good and customers like it",
            { style: 'professional', tone: 'confident' }
        );
        console.log('Rewritten content:', rewritten.rewritten_content);
        
        // Example 3: Get model recommendation
        const recommendation = await client.getModelRecommendation('technical_analysis');
        console.log('Recommended model:', recommendation.recommended_model);
        
        // Example 4: Health check
        const health = await client.healthCheck();
        console.log('API Status:', health.status);
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Run examples
// examples();

module.exports = GrowerAIClient;
```

### Browser JavaScript (Fetch API)
```javascript
class GrowerAIBrowserClient {
    constructor(baseUrl = 'http://localhost:8000', heliconeApiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.headers = {
            'Content-Type': 'application/json'
        };
        
        if (heliconeApiKey) {
            this.headers['Helicone-Auth'] = `Bearer ${heliconeApiKey}`;
            this.headers['Helicone-Cache-Enabled'] = 'true';
        }
    }
    
    async generateContent(prompt, options = {}) {
        const payload = {
            data: prompt,
            operation: 'content_generation',
            parameters: {
                model: options.model || 'azure/gpt-4o',
                max_tokens: options.maxTokens || 1000,
                temperature: options.temperature || 0.7,
                use_case: options.useCase || 'general'
            }
        };
        
        const response = await fetch(`${this.baseUrl}/process`, {
            method: 'POST',
            headers: {
                ...this.headers,
                'Helicone-Property-Use-Case': options.useCase || 'general'
            },
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    async healthCheck() {
        const response = await fetch(`${this.baseUrl}/health`, {
            headers: this.headers
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
}

// Browser usage example
const client = new GrowerAIBrowserClient();

client.generateContent("Write a brief introduction to machine learning")
    .then(result => console.log(result.result))
    .catch(error => console.error('Error:', error));

## 🔧 cURL Examples

### Basic Content Generation
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -H "Helicone-Auth: Bearer your_helicone_api_key" \
  -H "Helicone-Property-Use-Case: content_generation" \
  -d '{
    "data": "Write a professional email announcing a new product launch",
    "operation": "content_generation",
    "parameters": {
      "model": "azure/gpt-4o",
      "max_tokens": 500,
      "temperature": 0.7,
      "use_case": "business_communication"
    }
  }'
```

### Content Repurposing
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -H "Helicone-Auth: Bearer your_helicone_api_key" \
  -d '{
    "data": {
      "content": "We have a new feature that makes things better for users",
      "tone": "professional",
      "target_audience": "enterprise_customers",
      "model_name": "azure/gpt-4o"
    },
    "operation": "repurpose_content"
  }'
```

### Model Recommendation
```bash
curl -X GET "http://localhost:8000/models/recommended/creative_writing" \
  -H "Helicone-Auth: Bearer your_helicone_api_key"
```

### Model Comparison
```bash
curl -X POST "http://localhost:8000/models/compare" \
  -H "Content-Type: application/json" \
  -H "Helicone-Auth: Bearer your_helicone_api_key" \
  -d '{
    "prompt": "Explain the benefits of cloud computing for small businesses",
    "models": ["azure/gpt-4o", "azure/gpt-4.1", "azure/deepseek"],
    "evaluation_criteria": ["clarity", "accuracy", "business_relevance"],
    "include_cost_analysis": true
  }'
```

### Health Check
```bash
curl -X GET "http://localhost:8000/health" \
  -H "Helicone-Auth: Bearer your_helicone_api_key"
```

### Monitoring Summary
```bash
curl -X GET "http://localhost:8000/monitoring/summary" \
  -H "Helicone-Auth: Bearer your_helicone_api_key"
```

## 🔍 Error Handling Best Practices

### Python Error Handling
```python
import requests
from requests.exceptions import RequestException, HTTPError, Timeout

def robust_api_call(client, prompt, max_retries=3):
    """Make API call with robust error handling and retries."""

    for attempt in range(max_retries):
        try:
            result = client.generate_content(prompt)
            return result

        except HTTPError as e:
            if e.response.status_code == 429:  # Rate limit
                wait_time = 2 ** attempt  # Exponential backoff
                print(f"Rate limited. Waiting {wait_time}s before retry...")
                time.sleep(wait_time)
                continue
            elif e.response.status_code >= 500:  # Server error
                print(f"Server error: {e.response.status_code}. Retrying...")
                continue
            else:
                raise  # Client error, don't retry

        except Timeout:
            print(f"Request timeout. Attempt {attempt + 1}/{max_retries}")
            continue

        except RequestException as e:
            print(f"Request failed: {e}")
            if attempt == max_retries - 1:
                raise

    raise Exception(f"Failed after {max_retries} attempts")
```

### JavaScript Error Handling
```javascript
async function robustApiCall(client, prompt, maxRetries = 3) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            return await client.generateContent(prompt);

        } catch (error) {
            if (error.response?.status === 429) {  // Rate limit
                const waitTime = Math.pow(2, attempt) * 1000;
                console.log(`Rate limited. Waiting ${waitTime}ms...`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
                continue;
            }

            if (error.response?.status >= 500) {  // Server error
                console.log(`Server error: ${error.response.status}. Retrying...`);
                continue;
            }

            throw error;  // Client error, don't retry
        }
    }

    throw new Error(`Failed after ${maxRetries} attempts`);
}
```

## 📊 Performance Optimization

### Batch Processing
```python
# Python batch processing example
async def process_batch(client, prompts, batch_size=5):
    """Process prompts in batches to optimize performance."""
    results = []

    for i in range(0, len(prompts), batch_size):
        batch = prompts[i:i + batch_size]
        batch_results = await asyncio.gather(*[
            client.generate_content(prompt) for prompt in batch
        ])
        results.extend(batch_results)

        # Small delay between batches to avoid rate limits
        await asyncio.sleep(0.1)

    return results
```

### Caching Strategy
```python
import hashlib
import json
from functools import wraps

def cache_response(ttl_seconds=3600):
    """Decorator to cache API responses."""
    cache = {}

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from function arguments
            cache_key = hashlib.md5(
                json.dumps([args, kwargs], sort_keys=True).encode()
            ).hexdigest()

            # Check cache
            if cache_key in cache:
                cached_result, timestamp = cache[cache_key]
                if time.time() - timestamp < ttl_seconds:
                    return cached_result

            # Make API call and cache result
            result = func(*args, **kwargs)
            cache[cache_key] = (result, time.time())
            return result

        return wrapper
    return decorator

# Usage
@cache_response(ttl_seconds=1800)  # 30 minutes
def cached_generate_content(client, prompt, model):
    return client.generate_content(prompt, model=model)
```

## 🔐 Security Best Practices

### Environment Variables
```bash
# .env file
API_BASE_URL=http://localhost:8000
HELICONE_API_KEY=your_helicone_api_key_here

# Never commit API keys to version control
# Add .env to .gitignore
```

### Input Validation
```python
def validate_input(prompt, max_length=10000):
    """Validate user input before sending to API."""
    if not prompt or not isinstance(prompt, str):
        raise ValueError("Prompt must be a non-empty string")

    if len(prompt) > max_length:
        raise ValueError(f"Prompt too long. Maximum {max_length} characters")

    # Check for potentially harmful content
    forbidden_patterns = ['<script>', 'javascript:', 'data:']
    if any(pattern in prompt.lower() for pattern in forbidden_patterns):
        raise ValueError("Prompt contains potentially harmful content")

    return prompt.strip()
```

### Rate Limiting
```python
import time
from collections import deque

class RateLimiter:
    def __init__(self, max_requests=100, time_window=60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = deque()

    def can_make_request(self):
        now = time.time()

        # Remove old requests outside time window
        while self.requests and self.requests[0] < now - self.time_window:
            self.requests.popleft()

        return len(self.requests) < self.max_requests

    def record_request(self):
        self.requests.append(time.time())

# Usage
rate_limiter = RateLimiter(max_requests=50, time_window=60)

def rate_limited_api_call(client, prompt):
    if not rate_limiter.can_make_request():
        raise Exception("Rate limit exceeded. Please wait.")

    rate_limiter.record_request()
    return client.generate_content(prompt)
```

## 📈 Monitoring Integration

### Custom Metrics
```python
import time
from datetime import datetime

class APIMetrics:
    def __init__(self):
        self.request_count = 0
        self.total_response_time = 0
        self.error_count = 0
        self.model_usage = {}

    def record_request(self, model, response_time, success=True):
        self.request_count += 1
        self.total_response_time += response_time

        if not success:
            self.error_count += 1

        if model not in self.model_usage:
            self.model_usage[model] = 0
        self.model_usage[model] += 1

    def get_stats(self):
        return {
            "total_requests": self.request_count,
            "average_response_time": self.total_response_time / max(self.request_count, 1),
            "error_rate": self.error_count / max(self.request_count, 1),
            "model_usage": self.model_usage
        }

# Usage with client
metrics = APIMetrics()

def monitored_api_call(client, prompt, model="azure/gpt-4o"):
    start_time = time.time()
    success = True

    try:
        result = client.generate_content(prompt, model=model)
        return result
    except Exception as e:
        success = False
        raise
    finally:
        response_time = time.time() - start_time
        metrics.record_request(model, response_time, success)
```

## 🎯 Use Case Examples

### Marketing Content Generation
```python
def generate_marketing_content(client, product_name, target_audience, content_type):
    """Generate marketing content for different channels."""

    prompts = {
        "social_media": f"Create an engaging social media post for {product_name} targeting {target_audience}",
        "email": f"Write a compelling email subject line and preview for {product_name}",
        "blog": f"Generate a blog post outline about {product_name} benefits for {target_audience}",
        "ad_copy": f"Create persuasive ad copy for {product_name} focusing on key benefits"
    }

    if content_type not in prompts:
        raise ValueError(f"Unsupported content type: {content_type}")

    return client.generate_content(
        prompts[content_type],
        model="azure/grok-3",  # Best for creative content
        use_case="creative_writing",
        temperature=0.8
    )
```

### Technical Documentation
```python
def generate_technical_docs(client, api_endpoint, description):
    """Generate technical documentation for API endpoints."""

    prompt = f"""
    Generate comprehensive API documentation for the following endpoint:

    Endpoint: {api_endpoint}
    Description: {description}

    Include:
    - Endpoint description
    - Request/response examples
    - Error codes
    - Usage examples in multiple languages
    """

    return client.generate_content(
        prompt,
        model="azure/deepseek",  # Best for technical content
        use_case="technical_analysis",
        temperature=0.3,
        max_tokens=2000
    )
```

### Content Localization
```python
def localize_content(client, content, target_language, cultural_context):
    """Localize content for different markets."""

    prompt = f"""
    Localize the following content for {target_language} speakers:

    Original content: {content}

    Consider:
    - Cultural context: {cultural_context}
    - Local preferences and customs
    - Appropriate tone and style
    - Currency and measurement units
    """

    return client.generate_content(
        prompt,
        model="azure/llama-3.3",  # Best for multilingual content
        use_case="translation",
        temperature=0.5
    )
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Connection Errors
```python
# Check API connectivity
try:
    health = client.health_check()
    print("API Status:", health['status'])
except requests.ConnectionError:
    print("Cannot connect to API. Check if server is running.")
except requests.Timeout:
    print("Request timed out. Server may be overloaded.")
```

#### Authentication Issues
```bash
# Test API key validity
curl -X GET "http://localhost:8000/health" \
  -H "Helicone-Auth: Bearer your_api_key" \
  -v  # Verbose output for debugging
```

#### Model Availability
```python
# Check available models
models = client.get_available_models()
print("Available models:", [m['name'] for m in models['models']])
```

### Debug Mode
```python
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Add request/response logging to client
class DebugGrowerAIClient(GrowerAIClient):
    def generate_content(self, *args, **kwargs):
        logger.debug(f"Request: args={args}, kwargs={kwargs}")
        result = super().generate_content(*args, **kwargs)
        logger.debug(f"Response: {result}")
        return result
```
```
