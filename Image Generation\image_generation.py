import os
import json
import time
import logging
import requests
from typing import Optional, Dict, Any
from pathlib import Path
from datetime import datetime, timezone
from litellm import completion
from dotenv import load_dotenv
import sys

# Add the parent directory to the path to import from Model_call
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Model_call.model_manager import get_model_manager

# Import S3 utilities
from s3_utils import upload_image_to_s3

# Load environment variables from the .env file
BASE_DIR = Path(__file__).resolve().parent.parent
load_dotenv(dotenv_path=BASE_DIR / ".env")

# Setup logging
logger = logging.getLogger(__name__)

# Get the global model manager instance
model_manager = get_model_manager()



# Social Media Prompt Enhancement Templates
SOCIAL_MEDIA_ENHANCEMENTS = {
    "quality_modifiers": [
        "high resolution", "HD", "4K quality", "ultra-detailed", "crisp and clear",
        "professional quality", "sharp focus", "vibrant colors"
    ],
    "social_media_styles": [
        "professional social media ready", "Instagram-worthy", "LinkedIn professional",
        "social media optimized", "engaging visual content", "scroll-stopping image"
    ],
    "aspect_ratio_suggestions": {
        "square": "1:1 aspect ratio, perfect for Instagram posts",
        "landscape": "16:9 aspect ratio, ideal for LinkedIn and Facebook",
        "portrait": "9:16 aspect ratio, great for Instagram stories"
    },
    "lighting_enhancements": [
        "natural lighting", "soft lighting", "professional lighting",
        "bright and airy", "well-lit", "studio lighting"
    ]
}

def enhance_prompt_for_social_media(original_prompt: str, style_preference: str = "professional", max_length: int = None) -> str:
    """
    Enhance user prompts for social media optimization with quality and engagement modifiers.

    Args:
        original_prompt (str): The original user prompt
        style_preference (str): Style preference (professional, creative, casual)
        max_length (int): Maximum length for the enhanced prompt (for model-specific limits)

    Returns:
        str: Enhanced prompt optimized for social media
    """

    # Base enhancements
    quality_modifier = "high resolution, HD, 4K quality, professional quality"
    social_media_modifier = "professional social media ready, engaging visual content"
    lighting_modifier = "natural lighting, well-lit"

    # Style-specific enhancements
    style_enhancements = {
        "professional": "clean, modern, business-appropriate",
        "creative": "artistic, innovative, eye-catching",
        "casual": "friendly, approachable, relatable"
    }

    style_modifier = style_enhancements.get(style_preference, style_enhancements["professional"])

    # Construct enhanced prompt
    enhanced_prompt = f"{original_prompt}, {quality_modifier}, {social_media_modifier}, {style_modifier}, {lighting_modifier}"

    # If max_length is specified and prompt is too long, truncate intelligently
    if max_length and len(enhanced_prompt) > max_length:
        logger.warning(f"Enhanced prompt ({len(enhanced_prompt)} chars) exceeds max length ({max_length}). Truncating...")

        # Try with shorter enhancements
        short_quality = "HD, 4K quality"
        short_social = "social media ready"
        short_lighting = "well-lit"

        short_enhanced = f"{original_prompt}, {short_quality}, {short_social}, {style_modifier}, {short_lighting}"

        if len(short_enhanced) <= max_length:
            enhanced_prompt = short_enhanced
        else:
            # If still too long, just add minimal enhancement
            minimal_enhancement = "HD, professional"
            minimal_enhanced = f"{original_prompt}, {minimal_enhancement}"

            if len(minimal_enhanced) <= max_length:
                enhanced_prompt = minimal_enhanced
            else:
                # Last resort: truncate original prompt and add minimal enhancement
                available_space = max_length - len(minimal_enhancement) - 2  # 2 for ", "
                if available_space > 0:
                    truncated_prompt = original_prompt[:available_space].rstrip()
                    enhanced_prompt = f"{truncated_prompt}, {minimal_enhancement}"
                else:
                    # If even minimal enhancement doesn't fit, just truncate original
                    enhanced_prompt = original_prompt[:max_length]

        logger.info(f"Truncated enhanced prompt to {len(enhanced_prompt)} characters")

    logger.info(f"Enhanced prompt: {enhanced_prompt}")
    return enhanced_prompt

def generate_image_dalle3_litellm(prompt: str, model_name: str = "azure/dall-e-3") -> Dict[str, Any]:
    """
    Generate image using DALL-E 3 via LiteLLM with Helicone logging.

    Args:
        prompt (str): Enhanced prompt for image generation
        model_name (str): Model name for DALL-E 3

    Returns:
        Dict[str, Any]: Response containing image URL and metadata
    """
    try:
        logger.info(f"Generating image with DALL-E 3 via LiteLLM: {model_name}")

        # Use ModelManager for enhanced model calling with Helicone logging
        response = model_manager.call_image_model(
            prompt=prompt,
            model_name=model_name,
            size="1024x1024",
            quality="hd",
            n=1
        )

        return {
            "success": True,
            "image_url": response.get("image_url"),
            "model_used": "dall-e-3",
            "generation_method": "litellm"
        }

    except Exception as e:
        logger.error(f"DALL-E 3 generation failed: {str(e)}")
        raise Exception(f"DALL-E 3 image generation failed: {str(e)}")

def generate_image_bria_direct(prompt: str) -> Dict[str, Any]:
    """
    Generate image using Bria-2-3-Fast-gen2 via direct Azure API.

    This function uses the proven direct API approach that was working before.

    Args:
        prompt (str): Enhanced prompt for image generation (will be truncated to 256 chars if needed)

    Returns:
        Dict[str, Any]: Response containing base64 image data and metadata
    """
    try:
        logger.info("Generating image with Bria-2-3-Fast-gen2 via direct Azure API")

        # Bria API has a 256 character limit for prompts
        BRIA_MAX_PROMPT_LENGTH = 256
        if len(prompt) > BRIA_MAX_PROMPT_LENGTH:
            logger.warning(f"Prompt too long ({len(prompt)} chars), truncating to {BRIA_MAX_PROMPT_LENGTH} chars")
            prompt = prompt[:BRIA_MAX_PROMPT_LENGTH].rstrip()
            logger.info(f"Truncated prompt: {prompt}")

        # Bria API endpoint and headers
        bria_endpoint = "https://Bria-2-3-Fast-gen2.eastus2.models.ai.azure.com/images/generations"
        bria_api_key = os.getenv("BRIA_API_KEY")

        if not bria_api_key:
            raise Exception("BRIA_API_KEY not found in environment variables")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {bria_api_key}"
        }

        # Bria API request body
        payload = {
            "prompt": prompt,
            "size": "1024x1024",
            "num_inference_steps": 10,
            "text_guidance": 2.5,
            "negative_prompt": "stormy weather, dark clouds, blurry, low quality, distorted",
            "image_format": "png",
            "seed": int(time.time()) % 1000  # Dynamic seed based on timestamp
        }

        logger.info(f"Calling Bria API with payload: {payload}")

        # Make the API request
        response = requests.post(bria_endpoint, headers=headers, json=payload, timeout=120)

        if response.status_code == 200:
            response_data = response.json()
            logger.info("✅ Bria API call successful")

            # Extract base64 image data
            if "data" in response_data and len(response_data["data"]) > 0:
                base64_image = response_data["data"][0].get("b64_json")
                if base64_image:
                    logger.info("✅ Bria image generation successful via direct API")
                    return {
                        "success": True,
                        "base64_image": base64_image,
                        "model_used": "bria-2-3-fast-gen2",
                        "generation_method": "direct_api"
                    }

            raise Exception("No image data received from Bria API")
        else:
            error_msg = f"Bria API request failed with status {response.status_code}: {response.text}"
            logger.error(error_msg)
            raise Exception(error_msg)

    except requests.exceptions.RequestException as e:
        error_message = f"Bria API request failed: {str(e)}"
        logger.error(error_message)
        raise Exception(error_message)
    except Exception as e:
        error_message = f"Bria image generation failed: {str(e)}"
        logger.error(error_message)
        raise Exception(error_message)





def generate_unique_filename(model_name: str, extension: str = "png") -> str:
    """
    Generate a unique filename for the image.

    Args:
        model_name (str): Name of the model used
        extension (str): File extension

    Returns:
        str: Unique filename
    """
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    model_short = model_name.replace("azure/", "").replace("-", "_")
    return f"generated_image_{model_short}_{timestamp}.{extension}"

def image_generation(prompt: str, model_name: str = "dall-e-3") -> Dict[str, Any]:
    """
    Main image generation function with multi-model support, prompt enhancement, and S3 storage.

    Args:
        prompt (str): Original user prompt
        model_name (str): Model to use for generation ("dall-e-3", "bria-2-3-fast-gen2", or "bria")

    Returns:
        Dict[str, Any]: Comprehensive response with S3 URL and metadata
    """
    start_time = time.time()

    try:
        logger.info(f"Starting image generation with model: {model_name}")

        # Generate unique filename
        filename = generate_unique_filename(model_name)

        # Route to appropriate model with model-specific prompt enhancement
        if model_name.lower() in ["dall-e-3", "azure/dall-e-3"]:
            logger.info("Generating image with DALL-E 3...")
            # DALL-E 3 has no strict character limit, use full enhancement
            enhanced_prompt = enhance_prompt_for_social_media(prompt, "professional")

            # Use DALL-E 3 via LiteLLM
            generation_result = generate_image_dalle3_litellm(enhanced_prompt, "azure/dall-e-3")

            # Upload to S3
            s3_url = upload_image_to_s3(
                image_data=generation_result["image_url"],
                filename=filename,
                is_base64=False
            )

        elif model_name.lower() in ["bria-2-3-fast-gen2", "bria"]:
            logger.info("Using Bria image generation...")
            # Bria has 256 character limit, use constrained enhancement
            enhanced_prompt = enhance_prompt_for_social_media(prompt, "professional", max_length=256)

            # Use Bria via direct API (with fallback built-in)
            generation_result = generate_image_bria_direct(enhanced_prompt)

            # Upload to S3
            s3_url = upload_image_to_s3(
                image_data=generation_result["base64_image"],
                filename=filename,
                is_base64=True
            )

        else:
            raise ValueError(f"Unsupported model: {model_name}. Supported models: dall-e-3, bria-2-3-fast-gen2, bria")

        # Calculate generation time
        generation_time = time.time() - start_time

        # Prepare comprehensive response
        response = {
            "success": True,
            "image_url": s3_url,
            "model_used": generation_result["model_used"],
            "enhanced_prompt": enhanced_prompt,
            "metadata": {
                "original_prompt": prompt,
                "generation_time": f"{generation_time:.2f} seconds",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "filename": filename,
                "generation_method": generation_result["generation_method"]
            }
        }

        logger.info(f"Image generation completed successfully in {generation_time:.2f}s")
        return response

    except Exception as e:
        generation_time = time.time() - start_time
        logger.error(f"Image generation failed after {generation_time:.2f}s: {str(e)}")

        return {
            "success": False,
            "error": str(e),
            "model_used": model_name,
            "enhanced_prompt": enhanced_prompt if 'enhanced_prompt' in locals() else prompt,
            "metadata": {
                "original_prompt": prompt,
                "generation_time": f"{generation_time:.2f} seconds",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error_details": str(e)
            }
        }