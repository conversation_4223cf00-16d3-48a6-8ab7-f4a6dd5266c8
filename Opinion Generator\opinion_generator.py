import time
import json
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import os
from dotenv import load_dotenv
from Model_call.model_manager import get_model_manager
from post_processing_layer.post_proc import clean_and_complete_string
import logging

# Load environment variables
load_dotenv()

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize model manager
model_manager = get_model_manager()


def extract_article_content(url: str, timeout: int = 30) -> str:
    """
    Extract text content from a given article URL.
    
    Args:
        url (str): URL to extract content from
        timeout (int): Request timeout in seconds
        
    Returns:
        str: Extracted article text content
        
    Raises:
        Exception: If URL fetching or content extraction fails
    """
    try:
        logger.info(f"Fetching article content from URL: {url}")
        
        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError("Invalid URL format")
        
        # Set headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        # Fetch the webpage
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        # Parse HTML content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header", "aside", "iframe", "noscript"]):
            script.decompose()
        
        # Extract text from common article content containers
        content_selectors = [
            'article', 'main', '.content', '.post-content', '.entry-content',
            '.article-content', '.post-body', '.story-body', '.content-body',
            '.article-text', '.post-text', '.story-content', '.main-content'
        ]
        
        extracted_text = ""
        for selector in content_selectors:
            content_element = soup.select_one(selector)
            if content_element:
                extracted_text = content_element.get_text(strip=True, separator=' ')
                break
        
        # Fallback to body if no specific content container found
        if not extracted_text:
            body = soup.find('body')
            if body:
                extracted_text = body.get_text(strip=True, separator=' ')
        
        # Clean up the text
        extracted_text = re.sub(r'\s+', ' ', extracted_text)  # Replace multiple whitespace with single space
        extracted_text = re.sub(r'\n+', '\n', extracted_text)  # Replace multiple newlines with single newline
        
        if len(extracted_text.strip()) < 100:
            raise Exception("Insufficient content extracted from URL - article may be behind paywall or require JavaScript")
        
        logger.info(f"Successfully extracted {len(extracted_text)} characters from article")
        return extracted_text.strip()
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to fetch URL: {str(e)}")
        raise Exception(f"URL fetching failed: {str(e)}")
    except Exception as e:
        logger.error(f"Article content extraction failed: {str(e)}")
        raise Exception(f"Article content extraction failed: {str(e)}")


def create_opinion_prompt(article_content: str, stance: str, tone: str, platform: str) -> str:
    """
    Create a specialized prompt for generating opinion-based social media content.
    
    Args:
        article_content (str): The extracted article content
        stance (str): Either "agree" or "disagree"
        tone (str): Tone of voice for the opinion
        platform (str): Target platform optimization
        
    Returns:
        str: Formatted prompt for the AI model
    """
    
    # Platform-specific optimizations
    platform_guidelines = {
        "linkedin": "Professional networking context, thought leadership tone, industry insights focus",
        "twitter": "Concise, engaging, conversation-starting, hashtag-friendly",
        "facebook": "Community-oriented, discussion-friendly, personal storytelling elements",
        "general": "Versatile format suitable for multiple platforms"
    }
    
    platform_guide = platform_guidelines.get(platform.lower(), platform_guidelines["general"])
    
    stance_instruction = {
        "agree": "Express strong agreement and support for the article's main arguments, providing additional insights and reinforcing key points",
        "disagree": "Present thoughtful disagreement with the article's main arguments, offering alternative perspectives and counterpoints"
    }
    
    stance_guide = stance_instruction.get(stance.lower(), stance_instruction["agree"])
    
    prompt = f"""
You are an expert content creator who specializes in writing authentically human social media posts that express genuine opinions about articles. Your goal is to create a compelling opinion piece that sounds like it was written by a real person sharing their genuine thoughts and insights.

**ARTICLE CONTENT TO ANALYZE:**
{article_content[:3000]}  # Limit to prevent token overflow

**YOUR TASK:**
Write a {stance.upper()} opinion post about this article with the following requirements:

**STANCE:** {stance_guide}

**TONE:** {tone}

**PLATFORM OPTIMIZATION:** {platform_guide}

**CONTENT REQUIREMENTS:**
1. **Length:** 200-400 words - substantial enough to provide meaningful commentary
2. **Authenticity:** Write in a genuinely human voice that completely bypasses AI detection
3. **Grounded Analysis:** Base your opinion STRICTLY on information present in the article - do not add external facts or statistics
4. **Specific References:** Quote or reference specific points, data, or arguments from the article
5. **Personal Perspective:** Make it feel like a real person's genuine reaction and analysis
6. **Engaging Hook:** Start with an attention-grabbing opening that draws readers in
7. **Conversation Starter:** Include elements that encourage discussion and engagement

**WRITING STYLE GUIDELINES:**
- Use natural, conversational language with varied sentence structures
- Avoid corporate speak, buzzwords, and AI-typical phrases like "delve into", "leverage", "game-changer"
- Include rhetorical questions, personal observations, or relatable analogies
- Use contractions and informal language where appropriate for the tone
- Show genuine emotion and conviction in your stance
- Make it feel like someone sharing their authentic thoughts with their network

**CRITICAL OUTPUT REQUIREMENTS:**
- Return ONLY the social media post content
- NO prefacing text like "Here's a post" or "Here's my opinion"
- NO markdown formatting or separators
- Content should be immediately copy-paste ready for social media
- Start directly with the engaging hook

**REMEMBER:** Stay strictly within the bounds of information provided in the article. Your opinion should be a genuine human reaction to the specific content presented, not general commentary on the topic.

Write your {stance} opinion post now:"""

    return prompt


def model_call_for_opinion_generation(prompt: str, model_name: str = None) -> str:
    """
    Call the model for opinion generation with optimized parameters.
    
    Args:
        prompt (str): The formatted prompt
        model_name (str): Optional model name
        
    Returns:
        str: Generated opinion content
    """
    try:
        # Use model manager for consistent model calling
        result = model_manager.call_model(
            prompt=prompt,
            model_name=model_name,
            temperature=0.86,  # Higher temperature for more authentic, varied content
            max_tokens=1200,   # Sufficient for 200-400 word posts
            system_prompt="You are an expert social media content creator who writes authentically human posts that express genuine opinions about articles."
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Model call failed for opinion generation: {str(e)}")
        raise Exception(f"Opinion generation model call failed: {str(e)}")


def validate_opinion_request(data: dict) -> None:
    """
    Validate the opinion generation request data.

    Args:
        data (dict): Request data to validate

    Raises:
        ValueError: If required fields are missing or invalid
    """
    required_fields = ["url", "stance"]

    for field in required_fields:
        if field not in data:
            raise ValueError(f"Missing required field: '{field}'")

    # Validate stance
    valid_stances = ["agree", "disagree"]
    if data["stance"].lower() not in valid_stances:
        raise ValueError(f"Invalid stance: '{data['stance']}'. Must be one of: {', '.join(valid_stances)}")

    # Validate URL format
    url = data["url"]
    parsed_url = urlparse(url)
    if not parsed_url.scheme or not parsed_url.netloc:
        raise ValueError(f"Invalid URL format: '{url}'")

    # Validate optional fields if provided
    if "tone" in data and not isinstance(data["tone"], str):
        raise ValueError("Tone must be a string")

    if "platform" in data and not isinstance(data["platform"], str):
        raise ValueError("Platform must be a string")


def opinion_generator(
    url: str,
    stance: str,
    tone: str = "professional",
    platform: str = "general",
    model_name: str = None
) -> list:
    """
    Generate authentic, human-written social media posts expressing opinions about articles.

    Args:
        url (str): The article URL to analyze and form an opinion about
        stance (str): Either "agree" or "disagree" - determines support or opposition stance
        tone (str): Tone of voice for the opinion (default: "professional")
        platform (str): Target platform optimization (default: "general")
        model_name (str): Optional model name for content generation

    Returns:
        list: List containing the generated opinion post in the standard format

    Raises:
        Exception: If URL fetching, content extraction, or generation fails
    """
    try:
        logger.info(f"Starting opinion generation for URL: {url} with stance: {stance}")

        # Extract article content from URL
        article_content = extract_article_content(url)

        # Create specialized prompt for opinion generation
        prompt = create_opinion_prompt(article_content, stance, tone, platform)

        # Generate opinion content using the model
        response = model_call_for_opinion_generation(prompt, model_name)

        # Clean the response using existing post-processing
        cleaned_response = clean_and_complete_string(response)

        # Format response consistently with other content modules
        response_list = [cleaned_response]
        json_data = [{"text": text} for text in response_list]

        logger.info(f"Successfully generated opinion post with {len(cleaned_response)} characters")
        return json_data

    except Exception as e:
        logger.error(f"Opinion generation failed: {str(e)}")
        raise Exception(f"Opinion generation failed: {str(e)}")


# Production-ready module - no test functions or hardcoded URLs
# For testing, use the dedicated test scripts or API endpoints
