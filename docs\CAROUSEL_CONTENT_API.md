# Carousel Content Generation API

Comprehensive API documentation for the carousel content generation endpoint with three distinct generation modes and authentic human-written content optimization.

## 🎯 Overview

The carousel content generator creates engaging, multi-slide social media content that feels authentically human-written and bypasses AI detection patterns. It supports three generation modes:

- **Topic-Based Generation** (`from_topic`) - Generate carousel content from a given topic
- **Article-Based Generation** (`from_article`) - Transform article content into carousel format
- **Text-Based Generation** (`from_text`) - Convert provided text into carousel slides

## 🔗 Endpoint

```
POST /process
```

## 📋 Request Format

### Basic Structure
```json
{
  "data": {
    "generation_mode": "from_topic|from_article|from_text",
    "number_of_slides": 3-10,
    "tone": "Informative|Engaging|Professional|Casual|Inspirational",
    "target_audience": "string",
    "model_name": "azure/gpt-4o|azure/gpt-4o-mini|azure/grok-3",
    // Mode-specific parameters below
  },
  "operation": "carousel_content"
}
```

### Mode-Specific Parameters

#### 1. Topic-Based Generation (`from_topic`)
```json
{
  "data": {
    "generation_mode": "from_topic",
    "topic": "Your topic here",
    "number_of_slides": 5,
    "tone": "Informative",
    "target_audience": "General"
  },
  "operation": "carousel_content"
}
```

#### 2. Article-Based Generation (`from_article`)
```json
{
  "data": {
    "generation_mode": "from_article",
    "article_url": "https://example.com/article",
    "number_of_slides": 5,
    "tone": "Professional",
    "target_audience": "Business Professionals"
  },
  "operation": "carousel_content"
}
```

#### 3. Text-Based Generation (`from_text`)
```json
{
  "data": {
    "generation_mode": "from_text",
    "text_content": "Your text content here...",
    "number_of_slides": 4,
    "tone": "Engaging",
    "target_audience": "Tech Enthusiasts"
  },
  "operation": "carousel_content"
}
```

## 📊 Response Format

### Success Response
```json
{
  "result": {
    "slides": [
      {
        "id": 1,
        "title": "Short Title",
        "subtitle": "Subtitle for first slide only",
        "description": "Engaging description content (35-50 words)"
      },
      {
        "id": 2,
        "title": "Content Point",
        "description": "Detailed explanation content (35-50 words)"
      },
      {
        "id": 3,
        "title": "Key Insight",
        "description": "Important insight or takeaway (35-50 words)"
      },
      {
        "id": 4,
        "title": "Final Thought",
        "description": "Strong conclusion and reflection (35-50 words)"
      }
    ]
  },
  "status": "success",
  "message": "Operation 'carousel_content' completed successfully"
}
```

### Error Response
```json
{
  "detail": "Error description",
  "status_code": 400
}
```

## 🎛️ Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `generation_mode` | string | Mode of generation: `from_topic`, `from_article`, or `from_text` |
| `number_of_slides` | integer | Number of slides to generate (3-10) |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `tone` | string | "Informative" | Content tone: Informative, Engaging, Professional, Casual, Inspirational |
| `target_audience` | string | "General" | Target audience for the content |
| `model_name` | string | "azure/gpt-4o" | AI model to use for generation |

### Mode-Specific Required Parameters

| Mode | Required Parameter | Type | Description |
|------|-------------------|------|-------------|
| `from_topic` | `topic` | string | Topic to create carousel content about |
| `from_article` | `article_url` | string | URL of article to extract content from |
| `from_text` | `text_content` | string | Text content to transform into carousel |

## 🎨 Content Guidelines

### Slide Structure
1. **First Slide (Title Slide)**:
   - Title: Maximum 3-4 words, punchy and attention-grabbing
   - Subtitle: 10-12 words that expand on the title (first slide only)
   - Description: 2-3 sentences (35-50 words) that hook the reader

2. **Content Slides (Middle Slides)**:
   - Title: Clear, specific point or insight (3-6 words)
   - Description: 2-3 sentences (35-50 words) explaining the concept

3. **Final Slide (Conclusion)**:
   - Title: Strong, memorable takeaway (3-5 words)
   - Description: 2-3 sentences (35-50 words) that reinforce key insights

### Content Quality Standards
- ✅ Authentically human-written voice that bypasses AI detection
- ✅ Natural conversation starters and storytelling elements
- ✅ Specific examples and relatable scenarios
- ✅ Varied sentence structure and length
- ❌ No corporate speak, buzzwords, or obvious AI patterns
- ❌ No promotional CTAs like "Join our community" or "Follow for more"
- ❌ No action-oriented language like "Explore", "Discover", "Unleash"

## 🔧 Technical Specifications

### Model Parameters (Mode-Specific Optimization)
- **Temperature**: 0.86 (consistent across all modes for creative, human-like content)
- **Max Tokens**: 3600 (sufficient for detailed carousel content)

**Topic Mode** (`from_topic`):
- **Top P**: 0.58 (higher creativity for ideation)
- **Frequency Penalty**: 0.68 (reduces repetition in creative content)
- **Presence Penalty**: 0.53 (encourages topic diversity)

**Article Mode** (`from_article`):
- **Top P**: 0.52 (more focused for content extraction)
- **Frequency Penalty**: 0.39 (allows natural content flow)
- **Presence Penalty**: 0.33 (maintains article coherence)

**Text Mode** (`from_text`):
- **Top P**: 0.52 (focused transformation)
- **Frequency Penalty**: 0.46 (balanced repetition control)
- **Presence Penalty**: 0.33 (preserves original content structure)

### Helicone Integration
All API calls are automatically logged to Helicone with proper headers:
- `Helicone-Auth: Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva`
- `Helicone-OpenAI-Api-Base`
- `Helicone-Property-Use-Case: carousel_content_generation`

### Model Selection
- **Default Model**: `azure/gpt-4o` (recommended for carousel content)
- **Supported Models**: All Azure OpenAI models (gpt-4o, gpt-4o-mini, grok-3, etc.)
- **Intelligent Routing**: Automatically routes to appropriate Azure endpoints

## 📝 Usage Examples

### Example 1: Topic-Based Carousel
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -H "Helicone-Auth: Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva" \
  -d '{
    "data": {
      "generation_mode": "from_topic",
      "topic": "Artificial Intelligence in Healthcare",
      "number_of_slides": 5,
      "tone": "Informative",
      "target_audience": "Healthcare Professionals",
      "model_name": "azure/gpt-4o"
    },
    "operation": "carousel_content"
  }'
```

### Example 2: Article-Based Carousel
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -H "Helicone-Auth: Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva" \
  -d '{
    "data": {
      "generation_mode": "from_article",
      "article_url": "https://example.com/tech-trends-2024",
      "number_of_slides": 6,
      "tone": "Engaging",
      "target_audience": "Tech Professionals"
    },
    "operation": "carousel_content"
  }'
```

### Example 3: Text-Based Carousel
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -H "Helicone-Auth: Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva" \
  -d '{
    "data": {
      "generation_mode": "from_text",
      "text_content": "Remote work has transformed the modern workplace...",
      "number_of_slides": 4,
      "tone": "Professional",
      "target_audience": "Business Leaders"
    },
    "operation": "carousel_content"
  }'
```

## ⚠️ Error Handling

### Common Validation Errors

| Error | Status Code | Description |
|-------|-------------|-------------|
| Missing required fields | 400 | `generation_mode` or `number_of_slides` not provided |
| Invalid generation mode | 400 | Mode not in: `from_topic`, `from_article`, `from_text` |
| Invalid slide count | 400 | `number_of_slides` not between 3-10 |
| Missing mode parameter | 400 | Required parameter for specific mode missing |
| URL fetch failed | 400 | Article URL inaccessible or invalid |
| Content extraction failed | 400 | Insufficient content extracted from URL |

### Example Error Response
```json
{
  "detail": "Invalid generation_mode. Must be one of: from_topic, from_article, from_text",
  "status_code": 400
}
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
python tests/test_carousel_content.py
```

The test suite covers:
- All three generation modes
- Various model configurations
- Error handling and validation
- Content quality validation
- Performance benchmarking

## 🚀 Performance

- **Average Response Time**: 15-30 seconds (depending on model and content complexity)
- **Concurrent Requests**: Supported with proper rate limiting
- **Content Quality**: Optimized for authentic human-written style
- **Helicone Monitoring**: Full observability and analytics

## 📈 Best Practices

1. **Choose the Right Mode**:
   - Use `from_topic` for creative ideation
   - Use `from_article` for content repurposing
   - Use `from_text` for strict content transformation

2. **Optimize Parameters**:
   - Use 4-6 slides for optimal engagement
   - Match tone to your audience
   - Specify target audience for better relevance

3. **Content Quality**:
   - Review generated content for brand alignment
   - Customize tone and audience for best results
   - Use specific, relevant topics for better output

## 🔗 Related APIs

- [Content Generation API](./api_reference.md)
- [Image Generation API](./IMAGE_GENERATION_README.md)
- [Content Repurposing API](./REPURPOSE_CONTENT_API.md)
