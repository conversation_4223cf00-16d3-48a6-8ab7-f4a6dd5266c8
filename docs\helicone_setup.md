# Helicone Monitoring Setup Guide

Complete guide for setting up Helicone monitoring and observability for the Enhanced FastAPI application with Azure OpenAI integration.

## 🔍 Overview

Helicone provides comprehensive monitoring, caching, and observability for LLM applications. This integration enables:

- **Request/Response Logging** - Complete audit trail of all model interactions
- **Performance Monitoring** - Response times, token usage, and success rates
- **Cost Tracking** - Detailed cost analysis across models and endpoints
- **Caching** - Intelligent response caching to reduce costs and latency
- **Rate Limiting** - Protect against quota exhaustion and unexpected usage spikes
- **Custom Analytics** - Business-specific metrics and dashboards

## 🚀 Quick Setup

### 1. Create Helicone Account
1. Visit [Helicone.ai](https://helicone.ai) and create an account
2. Navigate to the API Keys section
3. Generate a new API key for your application
4. Copy the API key for environment configuration

### 2. Install Dependencies
```bash
pip install helicone>=1.0.0
```

### 3. Environment Configuration
Add the following environment variables to your `.env` file:

```bash
# Required
HELICONE_API_KEY=your_helicone_api_key_here

# Optional Configuration
HELICONE_BASE_URL=https://api.helicone.ai
HELICONE_CACHE_ENABLED=true
HELICONE_RETRY_ENABLED=true
HELICONE_RETRY_COUNT=3
HELICONE_USER_ID=your_user_id
HELICONE_SESSION_NAME=growero-ai-session
HELICONE_ENVIRONMENT=production
HELICONE_RATE_LIMIT_POLICY=100_per_minute
```

### 4. Verify Integration
Test the integration with a simple API call:

```bash
curl -X GET "http://localhost:8000/health" \
  -H "accept: application/json"
```

Check the response for `helicone_status` to confirm integration:
```json
{
  "helicone_status": {
    "enabled": true,
    "last_sync": "2025-06-29T10:30:00Z",
    "cache_enabled": true
  }
}
```

## 📊 Dashboard Configuration

### 1. Access Helicone Dashboard
1. Log into your Helicone account
2. Navigate to the Dashboard section
3. You should see requests appearing from your application

### 2. Custom Properties Setup
The application automatically sends custom properties for enhanced filtering:

| Property | Description | Example Values |
|----------|-------------|----------------|
| `Environment` | Deployment environment | `production`, `staging`, `development` |
| `Model-Type` | Type of model service | `azure-openai` |
| `Use-Case` | Application use case | `content_generation`, `creative_writing` |
| `Model-Name` | Specific model used | `azure/gpt-4o`, `azure/gpt-4.1` |
| `Endpoint` | Azure endpoint region | `eastus2`, `westeurope` |
| `Cost-Tier` | Model cost category | `premium`, `standard`, `budget` |

### 3. Recommended Dashboard Views

#### Model Performance Dashboard
Create filters for:
- **By Model**: `Helicone-Property-Model-Name`
- **By Endpoint**: `Helicone-Property-Endpoint`
- **By Cost Tier**: `Helicone-Property-Cost-Tier`

#### Use Case Analytics
Create filters for:
- **By Use Case**: `Helicone-Property-Use-Case`
- **By Environment**: `Helicone-Property-Environment`

#### Cost Optimization Dashboard
Track:
- Cost per model
- Cache hit rates
- Token usage patterns
- Response time trends

## 🔧 Advanced Configuration

### 1. Caching Configuration
Enable intelligent caching to reduce costs and improve response times:

```python
# Environment variables
HELICONE_CACHE_ENABLED=true
HELICONE_CACHE_TTL=3600  # 1 hour cache TTL
```

**Cache Strategy:**
- **Content Generation**: 30-minute cache for similar prompts
- **Model Comparisons**: 1-hour cache for identical requests
- **Health Checks**: 5-minute cache for status endpoints

### 2. Rate Limiting Setup
Protect against quota exhaustion:

```python
# Environment variables
HELICONE_RATE_LIMIT_POLICY=100_per_minute
HELICONE_RATE_LIMIT_WINDOW=60
```

**Rate Limit Policies:**
- **Development**: 50 requests/minute
- **Production**: 100 requests/minute
- **Premium**: 500 requests/minute

### 3. Custom Headers for Enhanced Tracking
Add custom headers to specific requests:

```python
import requests

headers = {
    "Helicone-Auth": "Bearer YOUR_API_KEY",
    "Helicone-User-Id": "user-123",
    "Helicone-Session-Id": "session-456",
    "Helicone-Property-Content-Type": "blog-post",
    "Helicone-Property-User-Tier": "premium",
    "Helicone-Property-Cost-Center": "marketing"
}

response = requests.post(
    "http://localhost:8000/process",
    json={"data": "content", "operation": "content_generation"},
    headers=headers
)
```

## 📈 Monitoring Best Practices

### 1. Key Metrics to Track
- **Success Rate**: Target >98% across all models
- **Response Time**: Monitor P95 latency by model
- **Cost per Request**: Track spending patterns
- **Cache Hit Rate**: Aim for >20% cache utilization
- **Error Patterns**: Identify and resolve common failures

### 2. Alerting Setup
Configure alerts for:
- **High Error Rate**: >5% failures in 5-minute window
- **Slow Response Times**: P95 > 10 seconds
- **Cost Spikes**: >50% increase in hourly costs
- **Quota Warnings**: >80% of monthly quota used

### 3. Regular Monitoring Tasks
- **Daily**: Review error logs and performance metrics
- **Weekly**: Analyze cost trends and optimization opportunities
- **Monthly**: Review model performance and usage patterns

## 🔍 Troubleshooting

### Common Issues

#### 1. Helicone Not Logging Requests
**Symptoms**: No requests appearing in Helicone dashboard

**Solutions**:
```bash
# Check API key configuration
curl -X GET "http://localhost:8000/config/validate"

# Verify environment variables
echo $HELICONE_API_KEY

# Check application logs
tail -f application.log | grep -i helicone
```

#### 2. High Cache Miss Rate
**Symptoms**: Cache hit rate <10%

**Solutions**:
- Review prompt variations and standardize where possible
- Increase cache TTL for stable content
- Implement prompt normalization

#### 3. Rate Limit Errors
**Symptoms**: 429 errors from Helicone

**Solutions**:
- Increase rate limit policy
- Implement request queuing
- Add exponential backoff

### Debug Mode
Enable debug logging for detailed troubleshooting:

```python
# Environment variables
HELICONE_DEBUG=true
LOG_LEVEL=DEBUG
```

## 🎯 Cost Optimization Strategies

### 1. Intelligent Caching
- Cache similar prompts for content generation
- Use semantic similarity for cache matching
- Implement cache warming for common requests

### 2. Model Selection Optimization
- Use cost-effective models for simple tasks
- Reserve premium models for complex reasoning
- Implement automatic model downgrading

### 3. Request Batching
- Batch similar requests when possible
- Use async processing for non-urgent tasks
- Implement request deduplication

## 📊 Analytics and Reporting

### 1. Custom Dashboards
Create specialized dashboards for:
- **Executive Summary**: High-level metrics and costs
- **Technical Operations**: Performance and error tracking
- **Business Intelligence**: Usage patterns and ROI

### 2. Automated Reports
Set up automated reports for:
- **Daily Operations**: Error summary and performance
- **Weekly Business**: Cost analysis and usage trends
- **Monthly Strategic**: Model performance and optimization opportunities

### 3. Data Export
Export data for external analysis:
- **CSV Export**: For spreadsheet analysis
- **API Integration**: For custom analytics platforms
- **Webhook Integration**: For real-time alerting

## 🔗 Integration Examples

### Python SDK Integration
```python
from helicone import Helicone

# Initialize Helicone client
helicone = Helicone(api_key="your_api_key")

# Track custom events
helicone.log_event("content_generated", {
    "model": "azure/gpt-4o",
    "tokens": 150,
    "use_case": "marketing"
})
```

### Webhook Configuration
```python
# Webhook endpoint for real-time monitoring
@app.post("/webhooks/helicone")
async def helicone_webhook(request: dict):
    # Process Helicone events
    event_type = request.get("type")
    if event_type == "request.completed":
        # Handle completed request
        pass
    elif event_type == "rate_limit.exceeded":
        # Handle rate limit alerts
        pass
```

## 📞 Support and Resources

### Documentation Links
- [Helicone Official Docs](https://docs.helicone.ai)
- [LiteLLM Integration Guide](https://docs.litellm.ai/docs/observability/helicone_integration)
- [Azure OpenAI Best Practices](https://docs.microsoft.com/azure/cognitive-services/openai/)

### Community Resources
- [Helicone Discord](https://discord.gg/helicone)
- [GitHub Issues](https://github.com/Helicone/helicone)
- [Community Forum](https://community.helicone.ai)

### Getting Help
1. Check the troubleshooting section above
2. Review Helicone documentation
3. Contact support with detailed error logs
4. Join the community for peer assistance
