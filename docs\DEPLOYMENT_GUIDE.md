# LiteLLM Azure OpenAI Integration - Deployment Guide

## Overview

This guide covers the deployment of the enhanced FastAPI application with LiteLLM integration for multiple Azure OpenAI endpoints. The system now supports intelligent model routing, fallback mechanisms, and comprehensive monitoring.

## 🚀 Quick Start

### 1. Prerequisites

- Python 3.8+
- Azure OpenAI service accounts (2 endpoints)
- MongoDB instance
- AWS credentials (for Bedrock fallback)

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd growero-ai

# Install dependencies
pip install -r requirements.txt

# Verify configuration
python config_validator.py

# Run tests
python test_runner.py
```

### 3. Environment Configuration

Copy the `.env` file and configure the following variables:

#### Required Azure OpenAI Endpoints

**East US 2 (Advanced Models):**
```env
AZURE_API_KEY_4_1_O1=your_eastus2_api_key
AZURE_API_BASE_4_1_O1=https://yash-m3j02ah5-eastus2.openai.azure.com
AZURE_API_VERSION_4_1_O1=2025-01-01-preview
```

**West Europe (Standard Models):**
```env
AZURE_API_KEY_OTHER=your_westeurope_api_key
AZURE_API_BASE_OTHER=https://yash-m4btkbft-westeurope.services.ai.azure.com
AZURE_API_VERSION_OTHER=2024-05-01-preview
```

#### Model Deployments
```env
GPT_4_1_DEPLOYMENT=gpt-4.1
O1_DEPLOYMENT=o1
GPT_4O_DEPLOYMENT=gpt4o
GROK_3_DEPLOYMENT=grok3
LLAMA_3_3_DEPLOYMENT=llama33
DEEPSEEK_DEPLOYMENT=deepseek
```

## 🏗️ Architecture Overview

### Model Routing Strategy

The system intelligently routes requests to appropriate Azure endpoints:

- **East US 2**: Advanced models (GPT-4.1, O1) for complex tasks
- **West Europe**: Standard models (GPT-4o, Grok-3, Llama-3.3, DeepSeek)
- **Legacy**: Fallback endpoint for basic models

### Key Components

1. **ModelManager**: Central orchestration for model selection and routing
2. **LiteLLMConfig**: Configuration management for multiple endpoints
3. **Monitoring**: Performance tracking and health monitoring
4. **ErrorHandler**: Intelligent error categorization and recovery

## 📊 Available Models

| Model | Endpoint | Use Case | Cost Tier |
|-------|----------|----------|-----------|
| azure/gpt-4.1 | East US 2 | Complex analysis, technical content | Premium |
| azure/o1 | East US 2 | Advanced reasoning | Premium |
| azure/gpt-4o | West Europe | General content, balanced performance | Standard |
| azure/grok-3 | West Europe | Creative content | Standard |
| azure/llama-3.3 | West Europe | Alternative model | Standard |
| azure/deepseek | West Europe | Code and technical content | Standard |
| azure/gpt-4o-mini | Legacy | Simple tasks, high volume | Budget |

## 🔧 API Endpoints

### Content Generation

- `POST /process` - Enhanced content generation with model selection
- `POST /rewrite-ai-post` - Content rewriting with multi-model support
- `POST /rewrite-ai-post-advanced` - Advanced rewriting with full customization

### Model Management

- `GET /models/available` - List all available models
- `POST /models/select-optimal` - Get optimal model for requirements
- `POST /models/compare` - Compare multiple models
- `GET /models/by-cost` - Models grouped by cost tier

### Monitoring & Analytics

- `GET /monitoring/summary` - Comprehensive monitoring data
- `GET /monitoring/model/{model_name}` - Model-specific performance
- `GET /monitoring/system-status` - Overall system health
- `GET /analytics/performance-comparison` - Cross-model performance
- `GET /analytics/usage-statistics` - Usage trends and statistics

## 🚦 Running the Application

### Development Mode

```bash
# Start the main application
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Start content rewrite service (separate)
uvicorn "Rewrite Content.content_rewrite:app" --reload --port 8001
```

### Production Mode

```bash
# Install production dependencies
pip install gunicorn

# Start with Gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000
CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

## 🔍 Testing & Validation

### Configuration Validation

```bash
python config_validator.py
```

### Comprehensive Testing

```bash
python test_runner.py
```

### Unit Tests

```bash
pytest tests/ -v
```

## 📈 Monitoring & Observability

### Health Checks

- Endpoint health monitoring every 5 minutes
- Automatic failover on endpoint failures
- Performance metrics tracking

### Key Metrics

- Model success rates
- Average response times
- Error categorization
- Cost optimization metrics
- Usage distribution

### Logging

Structured logging with:
- Request/response tracking
- Error categorization
- Performance metrics
- Security events

## 🛡️ Security Considerations

- API keys masked in logs
- Request validation enabled
- Rate limiting available
- Secure credential management

## 🔧 Troubleshooting

### Common Issues

1. **Configuration Errors**
   ```bash
   python config_validator.py
   ```

2. **Model Availability**
   ```bash
   curl http://localhost:8000/models/available
   ```

3. **Endpoint Health**
   ```bash
   curl http://localhost:8000/health/endpoints
   ```

### Performance Optimization

- Enable connection pooling
- Configure appropriate timeouts
- Use cost-effective model selection
- Monitor response times

## 📚 Usage Examples

### Basic Content Generation

```python
import requests

response = requests.post("http://localhost:8000/process", json={
    "data": {
        "topic": "Artificial Intelligence",
        "tone": "professional",
        "target_audience": "business leaders",
        "model_name": "azure/gpt-4o"
    },
    "operation": "short_content"
})
```

### Model Comparison

```python
response = requests.post("http://localhost:8000/models/compare", json={
    "prompt": "Explain quantum computing",
    "models": ["azure/gpt-4.1", "azure/gpt-4o", "azure/grok-3"],
    "temperature": 0.5,
    "max_tokens": 200
})
```

### Content Repurposing

```python
response = requests.post("http://localhost:8000/process", json={
    "data": {
        "content": "Companies are adopting digital technologies to transform their operations and improve efficiency.",
        "tone": "engaging",
        "target_audience": "executives",
        "model_name": "azure/gpt-4.1"
    },
    "operation": "repurpose_content"
})
```

## 🔄 Migration from Previous Version

1. Update environment variables with new endpoint configurations
2. Run configuration validation
3. Test model availability
4. Update client code to use new model names
5. Monitor performance and adjust as needed

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run diagnostic tools
3. Review logs and monitoring data
4. Contact the development team

---

**Version**: 2.0.0  
**Last Updated**: 2025-01-01  
**Compatibility**: Python 3.8+, FastAPI 0.104+, LiteLLM 1.0+
