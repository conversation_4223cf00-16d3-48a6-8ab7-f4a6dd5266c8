import logging
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import asyncio
from functools import wraps
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelMetrics:
    """Track metrics for model performance and usage."""
    
    def __init__(self):
        self.call_counts = defaultdict(int)
        self.success_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.response_times = defaultdict(list)
        self.recent_errors = defaultdict(lambda: deque(maxlen=100))
        self.endpoint_health = defaultdict(bool)
        self.last_health_check = {}
        
    def record_call(self, model_name: str, success: bool, response_time: float, error: Optional[str] = None):
        """Record a model call with its metrics."""
        self.call_counts[model_name] += 1
        
        if success:
            self.success_counts[model_name] += 1
        else:
            self.error_counts[model_name] += 1
            if error:
                self.recent_errors[model_name].append({
                    "timestamp": datetime.now().isoformat(),
                    "error": error
                })
        
        self.response_times[model_name].append(response_time)
        # Keep only last 1000 response times
        if len(self.response_times[model_name]) > 1000:
            self.response_times[model_name] = self.response_times[model_name][-1000:]
    
    def get_success_rate(self, model_name: str) -> float:
        """Get success rate for a model."""
        total_calls = self.call_counts[model_name]
        if total_calls == 0:
            return 0.0
        return self.success_counts[model_name] / total_calls
    
    def get_average_response_time(self, model_name: str) -> float:
        """Get average response time for a model."""
        times = self.response_times[model_name]
        if not times:
            return 0.0
        return sum(times) / len(times)
    
    def get_recent_errors(self, model_name: str, limit: int = 10) -> List[Dict]:
        """Get recent errors for a model."""
        return list(self.recent_errors[model_name])[-limit:]
    
    def get_model_health_score(self, model_name: str) -> float:
        """Calculate a health score for a model (0-100)."""
        success_rate = self.get_success_rate(model_name)
        avg_response_time = self.get_average_response_time(model_name)
        
        # Base score from success rate (0-70 points)
        score = success_rate * 70
        
        # Response time penalty (0-30 points)
        if avg_response_time > 0:
            # Penalty increases with response time
            time_penalty = min(30, avg_response_time / 10)  # 1 point per 100ms
            score += max(0, 30 - time_penalty)
        else:
            score += 30
        
        return min(100, score)

class ErrorHandler:
    """Enhanced error handling with categorization and recovery strategies."""
    
    def __init__(self):
        self.error_categories = {
            "rate_limit": ["rate limit", "quota", "throttle"],
            "authentication": ["unauthorized", "invalid key", "authentication"],
            "network": ["connection", "timeout", "network"],
            "model_error": ["model", "deployment", "not found"],
            "validation": ["validation", "invalid input", "bad request"],
            "server_error": ["internal server", "500", "502", "503"]
        }
        
    def categorize_error(self, error_message: str) -> str:
        """Categorize an error based on its message."""
        error_lower = error_message.lower()
        
        for category, keywords in self.error_categories.items():
            if any(keyword in error_lower for keyword in keywords):
                return category
        
        return "unknown"
    
    def get_recovery_strategy(self, error_category: str) -> Dict[str, Any]:
        """Get recovery strategy based on error category."""
        strategies = {
            "rate_limit": {
                "retry": True,
                "delay": 60,  # Wait 1 minute
                "fallback_model": True,
                "exponential_backoff": True
            },
            "authentication": {
                "retry": False,
                "delay": 0,
                "fallback_model": True,
                "check_credentials": True
            },
            "network": {
                "retry": True,
                "delay": 5,
                "fallback_model": True,
                "exponential_backoff": True
            },
            "model_error": {
                "retry": False,
                "delay": 0,
                "fallback_model": True,
                "check_model_availability": True
            },
            "validation": {
                "retry": False,
                "delay": 0,
                "fallback_model": False,
                "fix_input": True
            },
            "server_error": {
                "retry": True,
                "delay": 30,
                "fallback_model": True,
                "exponential_backoff": True
            }
        }
        
        return strategies.get(error_category, {
            "retry": True,
            "delay": 10,
            "fallback_model": True,
            "exponential_backoff": False
        })

class HealthMonitor:
    """Monitor health of endpoints and models."""
    
    def __init__(self, model_manager):
        self.model_manager = model_manager
        self.health_check_interval = 300  # 5 minutes
        self.last_health_check = {}
        self.endpoint_status = {}
        
    async def check_endpoint_health(self, endpoint_type: str) -> bool:
        """Check health of a specific endpoint."""
        try:
            # Get a model from this endpoint
            models = self.model_manager.get_models_by_endpoint(endpoint_type)
            if not models:
                return False
            
            # Try a simple call to test the endpoint
            test_model = models[0]
            start_time = time.time()
            
            result = self.model_manager.call_model(
                prompt="Test health check",
                model_name=test_model,
                max_tokens=10,
                temperature=0.1
            )
            
            response_time = time.time() - start_time
            
            # Consider healthy if response time < 30 seconds and we got a result
            is_healthy = response_time < 30 and bool(result)
            
            self.endpoint_status[endpoint_type] = {
                "healthy": is_healthy,
                "last_check": datetime.now().isoformat(),
                "response_time": response_time,
                "test_model": test_model
            }
            
            return is_healthy
            
        except Exception as e:
            logger.error(f"Health check failed for {endpoint_type}: {str(e)}")
            self.endpoint_status[endpoint_type] = {
                "healthy": False,
                "last_check": datetime.now().isoformat(),
                "error": str(e)
            }
            return False
    
    async def run_health_checks(self):
        """Run health checks for all endpoints."""
        endpoints = list(self.model_manager.config.endpoint_configs.keys())
        
        for endpoint in endpoints:
            try:
                await self.check_endpoint_health(endpoint)
            except Exception as e:
                logger.error(f"Error checking health for {endpoint}: {str(e)}")
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get a summary of endpoint health."""
        total_endpoints = len(self.endpoint_status)
        healthy_endpoints = sum(1 for status in self.endpoint_status.values() if status.get("healthy", False))
        
        return {
            "total_endpoints": total_endpoints,
            "healthy_endpoints": healthy_endpoints,
            "health_percentage": (healthy_endpoints / total_endpoints * 100) if total_endpoints > 0 else 0,
            "endpoint_details": self.endpoint_status,
            "last_check": max([status.get("last_check", "") for status in self.endpoint_status.values()], default="")
        }

# Global instances
model_metrics = ModelMetrics()
error_handler = ErrorHandler()
health_monitor = None  # Will be initialized with model_manager

def initialize_monitoring(model_manager):
    """Initialize monitoring with model manager."""
    global health_monitor
    health_monitor = HealthMonitor(model_manager)
    return health_monitor

def monitor_model_call(func):
    """Decorator to monitor model calls."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        model_name = kwargs.get('model_name', 'unknown')
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            response_time = time.time() - start_time
            model_metrics.record_call(model_name, True, response_time)
            return result
            
        except Exception as e:
            response_time = time.time() - start_time
            error_message = str(e)
            model_metrics.record_call(model_name, False, response_time, error_message)
            
            # Log error with category
            error_category = error_handler.categorize_error(error_message)
            logger.error(f"Model call failed - Model: {model_name}, Category: {error_category}, Error: {error_message}")
            
            raise
    
    return wrapper

def get_monitoring_summary() -> Dict[str, Any]:
    """Get comprehensive monitoring summary."""
    return {
        "metrics": {
            "total_calls": dict(model_metrics.call_counts),
            "success_rates": {model: model_metrics.get_success_rate(model) for model in model_metrics.call_counts.keys()},
            "average_response_times": {model: model_metrics.get_average_response_time(model) for model in model_metrics.call_counts.keys()},
            "health_scores": {model: model_metrics.get_model_health_score(model) for model in model_metrics.call_counts.keys()}
        },
        "health": health_monitor.get_health_summary() if health_monitor else {},
        "recent_errors": {model: model_metrics.get_recent_errors(model, 5) for model in model_metrics.recent_errors.keys()}
    }
