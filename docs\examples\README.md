# Code Examples

This directory contains practical code examples for integrating with the Enhanced FastAPI application.

## 📁 Structure

### Language-Specific Examples
- **[Python Examples](python/)** - Python integration samples
- **[JavaScript Examples](javascript/)** - Node.js and browser examples  
- **[cURL Examples](curl/)** - Command-line testing examples

### Use Case Examples
- **[Content Generation](use_cases/content_generation.md)** - Marketing and creative content
- **[Model Comparison](use_cases/model_comparison.md)** - Performance testing scenarios
- **[Monitoring Integration](use_cases/monitoring.md)** - Helicone setup examples

## 🚀 Quick Examples

### Basic Content Generation (Python)
```python
import requests

response = requests.post("http://localhost:8000/process", json={
    "data": "Write a blog post about AI",
    "operation": "content_generation",
    "parameters": {"model": "azure/gpt-4o", "max_tokens": 1000}
})
```

### Model Selection (JavaScript)
```javascript
const response = await fetch('http://localhost:8000/models/recommended/creative_writing');
const recommendation = await response.json();
```

### Health Check (cURL)
```bash
curl -X GET "http://localhost:8000/health" \
  -H "accept: application/json"
```

## 📊 Testing Scenarios

Each example includes:
- Complete request/response examples
- Error handling patterns
- Helicone integration headers
- Performance optimization tips
- Cost-effective model selection

## 🔧 Environment Setup

Before running examples, ensure you have:
1. API endpoint configured
2. Azure OpenAI credentials set
3. Helicone API key (optional)
4. Required dependencies installed
