{"timestamp": 1751224668.3228807, "execution_time": 22.190675497055054, "test_suites": [{"name": "Comprehensive Parameter Test", "script": "test_long_content_rewrite_parameters.py", "description": "Tests both parameters across all Azure OpenAI models"}, {"name": "West Europe Models Test", "script": "test_west_europe_models.py", "description": "Tests gpt-4o, grok-3, llama-3.3, deepseek models"}, {"name": "East US 2 Models Test", "script": "test_east_us2_models.py", "description": "Tests gpt-4.1, o1 models with special constraints"}], "suite_results": {"Comprehensive Parameter Test": {"success": false, "stdout": "", "stderr": "INFO:Model_call.model_manager:Helicone integration available via LiteLLM callbacks\r\nINFO:Model_call.model_manager:Helicone monitoring configured successfully with LiteLLM callbacks\r\nTraceback (most recent call last):\r\n  File \"D:\\Yash\\growero\\growero-ai\\tests\\test_long_content_rewrite_parameters.py\", line 315, in <module>\r\n    asyncio.run(main())\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 194, in run\r\n    return runner.run(main)\r\n           ^^^^^^^^^^^^^^^^\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\r\n    return self._loop.run_until_complete(task)\r\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 664, in run_until_complete\r\n    return future.result()\r\n           ^^^^^^^^^^^^^^^\r\n  File \"D:\\Yash\\growero\\growero-ai\\tests\\test_long_content_rewrite_parameters.py\", line 291, in main\r\n    print(\"\\U0001f680 Starting comprehensive test suite for long_content and rewrite_content parameters\")\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1252.py\", line 19, in encode\r\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\r\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f680' in position 0: character maps to <undefined>\r\n", "return_code": 1}, "West Europe Models Test": {"success": false, "stdout": "", "stderr": "INFO:Model_call.model_manager:Helicone integration available via LiteLLM callbacks\r\nINFO:Model_call.model_manager:Helicone monitoring configured successfully with LiteLLM callbacks\r\nTraceback (most recent call last):\r\n  File \"D:\\Yash\\growero\\growero-ai\\tests\\test_west_europe_models.py\", line 211, in <module>\r\n    asyncio.run(main())\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 194, in run\r\n    return runner.run(main)\r\n           ^^^^^^^^^^^^^^^^\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\r\n    return self._loop.run_until_complete(task)\r\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 664, in run_until_complete\r\n    return future.result()\r\n           ^^^^^^^^^^^^^^^\r\n  File \"D:\\Yash\\growero\\growero-ai\\tests\\test_west_europe_models.py\", line 190, in main\r\n    print(\"\\U0001f680 Starting West Europe Azure OpenAI Models Test Suite\")\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1252.py\", line 19, in encode\r\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\r\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f680' in position 0: character maps to <undefined>\r\n", "return_code": 1}, "East US 2 Models Test": {"success": false, "stdout": "", "stderr": "INFO:Model_call.model_manager:Helicone integration available via LiteLLM callbacks\r\nINFO:Model_call.model_manager:Helicone monitoring configured successfully with LiteLLM callbacks\r\nTraceback (most recent call last):\r\n  File \"D:\\Yash\\growero\\growero-ai\\tests\\test_east_us2_models.py\", line 244, in <module>\r\n    asyncio.run(main())\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 194, in run\r\n    return runner.run(main)\r\n           ^^^^^^^^^^^^^^^^\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py\", line 118, in run\r\n    return self._loop.run_until_complete(task)\r\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py\", line 664, in run_until_complete\r\n    return future.result()\r\n           ^^^^^^^^^^^^^^^\r\n  File \"D:\\Yash\\growero\\growero-ai\\tests\\test_east_us2_models.py\", line 221, in main\r\n    print(\"\\U0001f680 Starting East US 2 Azure OpenAI Models Test Suite\")\r\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1252.py\", line 19, in encode\r\n    return codecs.charmap_encode(input,self.errors,encoding_table)[0]\r\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\r\nUnicodeEncodeError: 'charmap' codec can't encode character '\\U0001f680' in position 0: character maps to <undefined>\r\n", "return_code": 1}}, "analysis": {"total_suites": 3, "successful_suites": 0, "failed_suites": 3, "suite_details": {}}, "version": "1.0.0", "features_tested": ["long_content parameter functionality", "rewrite_content parameter functionality", "Clean output implementation", "Azure OpenAI multi-model compatibility", "Parameter priority handling", "Helicone integration logging"]}