# Long Content & Rewrite Content Parameters Documentation

## Overview
This document describes the implementation of `long_content` and `rewrite_content` parameters for the social media content generation API. Both parameters follow the established clean output implementation pattern documented in CLEAN_OUTPUT_IMPLEMENTATION.md.

## Features Implemented

### 1. Long Content Parameter (`long_content`)
- **Purpose**: Enables generation of social media content from longer, more detailed input text
- **Functionality**: Identical to the existing `topic` parameter but designed for extended content input
- **Use Case**: When you have detailed content that needs to be condensed into engaging social media posts

### 2. Rewrite Content Parameter (`rewrite_content`)
- **Purpose**: Refines and optimizes existing content for better engagement and authenticity
- **Functionality**: Takes existing content and enhances it while maintaining the core message
- **Use Case**: When you have basic content that needs improvement for social media platforms

### 3. Clean Output Implementation
- **Zero Editing Required**: All generated content is immediately copy-paste ready
- **No Prefacing Text**: Eliminates introductory phrases like "Here's a LinkedIn post..."
- **No Separators**: Removes formatting artifacts like "---" or "***"
- **Immediate Engagement**: Content starts directly with scroll-stopping hooks

## Parameter Priority
When multiple content parameters are provided, the system follows this priority order:
1. `rewrite_content` (highest priority)
2. `long_content` 
3. `topic` (fallback)

## API Usage

### Basic Request Structure
```json
{
  "data": {
    "topic": "Fallback topic",
    "tone": "Professional",
    "target_audience": "Business Leaders",
    "model_name": "azure/gpt-4o",
    "long_content": "Optional longer content input",
    "rewrite_content": "Optional content to be refined"
  },
  "operation": "short_content"
}
```

### Example 1: Using Long Content Parameter
```json
{
  "data": {
    "topic": "Backup topic",
    "tone": "Engaging",
    "target_audience": "Technology Leaders",
    "model_name": "azure/gpt-4o",
    "long_content": "Cloud computing has revolutionized how businesses operate by providing scalable, on-demand access to computing resources. Organizations can now deploy applications globally, scale infrastructure automatically, and reduce operational costs significantly. The shift to cloud-first strategies enables innovation, improves disaster recovery, and supports remote work capabilities that are essential in today's digital economy."
  },
  "operation": "short_content"
}
```

**Expected Output** (Clean, copy-paste ready):
```
Ever wondered how cloud computing became the backbone of modern business? 🌐

Just 10 years ago, scaling infrastructure meant buying servers and hoping for the best.

Today? Companies deploy globally with a few clicks, scale automatically based on demand, and cut operational costs by 40-60%.

The real game-changer isn't just the technology—it's the mindset shift.

Cloud-first strategies don't just enable remote work; they enable remote possibilities.

What's your biggest cloud transformation win?

#CloudComputing #DigitalTransformation #TechLeadership
```

### Example 2: Using Rewrite Content Parameter
```json
{
  "data": {
    "topic": "Backup topic",
    "tone": "Casual",
    "target_audience": "Entrepreneurs",
    "model_name": "azure/gpt-4o",
    "rewrite_content": "Remote work is good for companies. It saves money and makes employees happy. People can work from home and be more productive. Technology helps teams stay connected. Many companies are keeping remote work policies."
  },
  "operation": "short_content"
}
```

**Expected Output** (Enhanced and optimized):
```
Plot twist: Remote work isn't just "good for companies"—it's reshaping the entire game 🏠💼

Remember when working from home felt like playing hooky?

Now it's the secret sauce for:
→ 25% cost savings (goodbye expensive office leases!)
→ Happier employees (work-life balance = retention gold)
→ Productivity that actually went UP (shocking, right?)

The tech stack that makes it possible? Slack, Zoom, Asana, and a whole ecosystem we couldn't imagine 5 years ago.

Best part? This isn't a "pandemic trend" anymore—it's the new normal.

Are you still fighting remote work or finally embracing it?

#RemoteWork #Entrepreneurship #FutureOfWork
```

### Example 3: Parameter Priority Test
```json
{
  "data": {
    "topic": "This will be ignored",
    "tone": "Professional",
    "target_audience": "Business Leaders",
    "model_name": "azure/gpt-4o",
    "long_content": "This will also be ignored",
    "rewrite_content": "Digital transformation is changing business. Companies use technology to improve operations."
  },
  "operation": "short_content"
}
```

The system will use `rewrite_content` (highest priority) and ignore both `topic` and `long_content`.

## Azure OpenAI Model Compatibility

### West Europe Endpoint Models
- **azure/gpt-4o**: Excellent for balanced content generation
- **azure/grok-3**: Best for creative and engaging content
- **azure/llama-3.3**: Strong performance for conversational tone
- **azure/deepseek**: Optimal for analytical and technical content

### East US 2 Endpoint Models
- **azure/gpt-4.1**: High-quality content with advanced reasoning (mapped from gpt-4.5)
- **azure/o1**: Superior for complex reasoning tasks (temperature restrictions handled automatically)

## Content Quality Standards

### Human-Like Authenticity Markers
- ✅ Natural conversational tone with contractions
- ✅ Genuine emotional expressions and reactions
- ✅ Specific examples instead of generic statements
- ✅ Personal insights and "aha moments"
- ✅ Relatable scenarios and vulnerability

### Platform Optimization
- **LinkedIn**: Professional insights, thought leadership, career advice
- **Twitter**: Concise threads, trending topics, viral hooks
- **Facebook**: Community stories, personal narratives, local engagement

### Engagement Optimization
- ✅ Scroll-stopping opening hooks
- ✅ Strategic emotional triggers
- ✅ Thought-provoking questions
- ✅ Relevant hashtags and trending topics
- ✅ Clear calls-to-action

## Testing and Validation

### Automated Testing
Run the comprehensive test suite to validate functionality:

```bash
# Run all tests
python tests/master_test_runner.py

# Test specific endpoints
python tests/test_west_europe_models.py
python tests/test_east_us2_models.py

# Comprehensive parameter testing
python tests/test_long_content_rewrite_parameters.py
```

### Manual Testing with Postman
Import the provided Postman collection (`postman`) for manual testing:
- Standard content generation (baseline)
- Long content parameter tests across all models
- Rewrite content parameter tests across all models
- Parameter priority validation
- Edge case testing

### Clean Output Validation
Every response is automatically validated for:
- ❌ No prefacing text ("Here's a LinkedIn post...")
- ❌ No separator lines ("---", "***", etc.)
- ✅ Immediate engaging hooks
- ✅ Copy-paste ready format

## Error Handling

### Model-Specific Constraints
- **O1 Models**: Temperature restrictions handled automatically
- **Token Limits**: Automatic truncation with quality preservation
- **Endpoint Failures**: Intelligent fallback to alternative models

### Parameter Validation
- Empty parameters fall back to lower priority parameters
- Invalid model names trigger automatic model selection
- Missing required fields return clear error messages

## Helicone Integration

All API calls are automatically logged to Helicone for monitoring:
- **Headers**: Helicone-Auth: Bearer sk-helicone-mvpj2ti-7htuyly-rkvdq3a-ii2fcva
- **Base URL**: Helicone-OpenAI-Api-Base header included
- **Tracking**: Complete request/response logging for analytics

## Best Practices

### For Long Content Parameter
1. **Optimal Length**: 200-500 words for best results
2. **Content Type**: Detailed articles, reports, or comprehensive explanations
3. **Structure**: Well-organized content with clear main points
4. **Context**: Include relevant background information

### For Rewrite Content Parameter
1. **Input Quality**: Basic content that needs enhancement
2. **Length**: 50-200 words works best for rewriting
3. **Clarity**: Clear core message that needs improvement
4. **Purpose**: Content that lacks engagement or authenticity

### General Guidelines
1. **Model Selection**: Choose models based on content type and creativity needs
2. **Tone Matching**: Ensure tone aligns with target audience expectations
3. **Platform Targeting**: Consider platform-specific optimization requirements
4. **Testing**: Always validate output with clean output standards

## Troubleshooting

### Common Issues
1. **Empty Responses**: Check model availability and authentication
2. **Prefacing Text**: Verify clean output processing is working
3. **Model Errors**: Review model-specific constraints and limitations
4. **Parameter Conflicts**: Understand parameter priority order

### Support Resources
- Test suite results for debugging
- Helicone logs for API call analysis
- Model manager for endpoint health checks
- Postman collection for manual validation

## Advanced Usage Examples

### Multi-Platform Content Strategy
```python
# Generate LinkedIn version
linkedin_request = {
    "data": {
        "topic": "Backup topic",
        "tone": "Professional",
        "target_audience": "Business Executives",
        "model_name": "azure/gpt-4o",
        "long_content": "Detailed business analysis content..."
    },
    "operation": "short_content"
}

# Generate Twitter version
twitter_request = {
    "data": {
        "topic": "Backup topic",
        "tone": "Casual",
        "target_audience": "Tech Community",
        "model_name": "azure/grok-3",
        "rewrite_content": "Previous LinkedIn post content..."
    },
    "operation": "short_content"
}
```

### Content Refinement Workflow
```python
# Step 1: Generate initial content
initial_content = generate_content(topic="AI in Healthcare")

# Step 2: Refine for better engagement
refined_content = {
    "data": {
        "tone": "Engaging",
        "target_audience": "Healthcare Professionals",
        "model_name": "azure/gpt-4o",
        "rewrite_content": initial_content
    },
    "operation": "short_content"
}
```

## Version Information
- **Implementation Version**: 1.0.0
- **Clean Output Standard**: Following CLEAN_OUTPUT_IMPLEMENTATION.md
- **Azure OpenAI Compatibility**: All configured deployments
- **LiteLLM Integration**: Unified interface for all models
