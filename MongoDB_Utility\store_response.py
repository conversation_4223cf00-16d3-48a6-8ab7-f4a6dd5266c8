from pymongo import MongoClient
from datetime import datetime, timezone
import os

# MongoDB Connection

def insert_into_mongodb_otg(user_topic_list, response_content, operation):
    """
    Connects to MongoDB, inserts the given data into a specified collection, 
    and handles any connection or insertion errors.

    Parameters:
    response_content (dict): The data (in dictionary format) to be inserted into the MongoDB collection. 
    user_topic_list (list): The list of topics selected by the user.
    operation (str): The operation performed on the content.
    The expected format is the content to be inserted as a document.
                            
    Returns:
    None: The function prints success or failure messages based on the operation outcome.
    
    Raises:
    Exception: If the connection to MongoDB or insertion fails, an error message is printed.
    
    Steps:
    1. Establish a connection to MongoDB using a connection string (URI).
    2. Access the specified database and collection.
    3. Insert the provided data (response_content) as a document into the collection.
    4. Print the inserted document's ID if successful, or print an error message if failure occurs.
    """

    try:
        uri = os.getenv("MONGO_URI")
        client = MongoClient(uri)  # Replace with your MongoDB URI if remote
        print("Connected to MongoDB successfully!")
    except Exception as e:
        print("Failed to connect to MongoDB:", e)

    # Specify the database and collection
    db = client["CT_test"]  # Replace  with your database name
    collection = db["meta_data"]  # Replace  with your collection name

    # Insert Data
    try:
        # Data to insert into the MongoDB
        res_data = {
            "user_topic_list":user_topic_list,
            "content":response_content ,
            "operation" : operation,
            "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + " UTC" # UTC timestamp
        }

        result = collection.insert_one(res_data)
        print(f"Data inserted with ID: {result.inserted_id}")
    except Exception as e:
        print("Failed to insert data:", e)


def insert_into_mongodb_short(topic, tone, target_audience, response_content, operation):
    """
    Connects to MongoDB, inserts the given data into a specified collection, 
    and handles any connection or insertion errors.

    Parameters:
    response_content (dict): The data (in dictionary format) to be inserted into the MongoDB collection. 
    topic (str): The topic of the content.
    tone (str): The tone of the content.
    target_audience (str): The target audience of the content.
    operation (str): The operation performed on the content.                       

    Returns:
    None: The function prints success or failure messages based on the operation outcome.
    
    Raises:
    Exception: If the connection to MongoDB or insertion fails, an error message is printed.
    
    Steps:
    1. Establish a connection to MongoDB using a connection string (URI).
    2. Access the specified database and collection.
    3. Insert the provided data (response_content) as a document into the collection.
    4. Print the inserted document's ID if successful, or print an error message if failure occurs.
    """

    try:
        uri = os.getenv("MONGO_URI")
        client = MongoClient(uri)  # Replace with your MongoDB URI if remote
        print("Connected to MongoDB successfully!")
    except Exception as e:
        print("Failed to connect to MongoDB:", e)

    # Specify the database and collection
    db = client["CT_test"]  # Replace  with your database name
    collection = db["meta_data"]  # Replace  with your collection name

    # Insert Data
    try:
        # Data to insert into the MongoDB
        res_data = {
            "topic":topic,
            "tone":tone,
            "target_audience":target_audience,
            "content":response_content ,
            "operation" : operation,
            "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + " UTC" # UTC timestamp
        }

        result = collection.insert_one(res_data)
        print(f"Data inserted with ID: {result.inserted_id}")
    except Exception as e:
        print("Failed to insert data:", e)



def insert_into_mongodb_long(topic, tone, target_audience, framework, response_content, operation):
    """
    Connects to MongoDB, inserts the given data into a specified collection, 
    and handles any connection or insertion errors.

    Parameters:
    response_content (dict): The data (in dictionary format) to be inserted into the MongoDB collection. 
    topic (str): The topic of the content.
    tone (str): The tone of the content.
    target_audience (str): The target audience of the content.
    operation (str): The operation performed on the content.  
    framework (str): The framework used for the content generation.                       

    Returns:
    None: The function prints success or failure messages based on the operation outcome.
    
    Raises:
    Exception: If the connection to MongoDB or insertion fails, an error message is printed.
    
    Steps:
    1. Establish a connection to MongoDB using a connection string (URI).
    2. Access the specified database and collection.
    3. Insert the provided data (response_content) as a document into the collection.
    4. Print the inserted document's ID if successful, or print an error message if failure occurs.
    """

    try:
        uri = os.getenv("MONGO_URI")
        client = MongoClient(uri)  # Replace with your MongoDB URI if remote
        print("Connected to MongoDB successfully!")
    except Exception as e:
        print("Failed to connect to MongoDB:", e)

    # Specify the database and collection
    db = client["CT_test"]  # Replace  with your database name
    collection = db["meta_data"]  # Replace  with your collection name

    # Insert Data
    try:
        # Data to insert into the MongoDB
        res_data = {
            "topic":topic,
            "tone":tone,
            "target_audience":target_audience,
            "content":response_content ,
            "framework":framework,
            "operation" : operation,
            "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + " UTC" # UTC timestamp
        }

        result = collection.insert_one(res_data)
        print(f"Data inserted with ID: {result.inserted_id}")
    except Exception as e:
        print("Failed to insert data:", e)


def insert_image_data_into_mongodb(prompt_instruction, image_url, operation):
    """
    Connects to MongoDB, inserts the generated image URL and its prompt into a specified collection, 
    and handles any connection or insertion errors.

    Parameters:
    prompt_instruction (str): The prompt used to generate the image.
    image_url (str): The URL of the generated image.
    operation (str): The operation performed (e.g., 'image_generation').

    Returns:
    None: The function prints success or failure messages based on the operation outcome.
    
    Raises:
    Exception: If the connection to MongoDB or insertion fails, an error message is printed.
    
    Steps:
    1. Establish a connection to MongoDB using a connection string (URI).
    2. Access the specified database and collection.
    3. Insert the provided data (prompt and image URL) as a document into the collection.
    4. Print the inserted document's ID if successful, or print an error message if failure occurs.
    """

    try:
        uri = os.getenv("MONGO_URI")
        client = MongoClient(uri)  # Replace with your MongoDB URI if remote
        print("Connected to MongoDB successfully!")
    except Exception as e:
        print("Failed to connect to MongoDB:", e)

    # Specify the database and collection
    db = client["CT_test"]  # Replace with your database name
    collection = db["image_data"]  # Collection name to store image-related data

    # Insert Data
    try:
        # Data to insert into MongoDB
        res_data = {
            "prompt_instruction": prompt_instruction,
            "image_url": image_url,
            "operation": operation,
            "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + " UTC"  # UTC timestamp
        }

        result = collection.insert_one(res_data)
        print(f"Data inserted with ID: {result.inserted_id}")
    except Exception as e:
        print("Failed to insert data:", e)

