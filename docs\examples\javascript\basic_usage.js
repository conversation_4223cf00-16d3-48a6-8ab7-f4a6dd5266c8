/**
 * Basic usage examples for the Enhanced FastAPI application with Azure OpenAI integration.
 * Demonstrates common use cases and best practices in JavaScript/Node.js.
 */

const axios = require('axios');

class GrowerAIClient {
    constructor(baseUrl = 'http://localhost:8000', heliconeApiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.client = axios.create({
            baseURL: this.baseUrl,
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 second timeout
        });
        
        // Setup Helicone headers if API key provided
        if (heliconeApiKey) {
            this.client.defaults.headers['Helicone-Auth'] = `Bearer ${heliconeApiKey}`;
            this.client.defaults.headers['Helicone-Cache-Enabled'] = 'true';
            this.client.defaults.headers['Helicone-Property-Client'] = 'javascript-example';
        }
    }
    
    async generateContent(prompt, options = {}) {
        const {
            model = 'azure/gpt-4o',
            useCase = 'general',
            maxTokens = 1000,
            temperature = 0.7
        } = options;
        
        const payload = {
            data: prompt,
            operation: 'content_generation',
            parameters: {
                model,
                max_tokens: maxTokens,
                temperature,
                use_case: useCase
            }
        };
        
        const headers = {
            'Helicone-Property-Use-Case': useCase
        };
        
        try {
            const response = await this.client.post('/process', payload, { headers });
            return response.data;
        } catch (error) {
            throw new Error(`Content generation failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async rewriteContent(content, options = {}) {
        const {
            style = 'professional',
            tone = 'engaging',
            model = 'azure/gpt-4o',
            targetAudience = 'business_professionals'
        } = options;
        
        const payload = {
            content,
            style,
            tone,
            target_audience: targetAudience,
            preserve_key_points: true,
            model
        };
        
        try {
            const response = await this.client.post('/rewrite-ai-post', payload);
            return response.data;
        } catch (error) {
            throw new Error(`Content rewriting failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async getModelRecommendation(useCase) {
        try {
            const response = await this.client.get(`/models/recommended/${useCase}`);
            return response.data;
        } catch (error) {
            throw new Error(`Model recommendation failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async compareModels(prompt, models, evaluationCriteria = ['accuracy', 'clarity', 'engagement']) {
        const payload = {
            prompt,
            models,
            evaluation_criteria: evaluationCriteria,
            include_cost_analysis: true
        };
        
        try {
            const response = await this.client.post('/models/compare', payload);
            return response.data;
        } catch (error) {
            throw new Error(`Model comparison failed: ${error.response?.data?.detail || error.message}`);
        }
    }
    
    async healthCheck() {
        try {
            const response = await this.client.get('/health');
            return response.data;
        } catch (error) {
            throw new Error(`Health check failed: ${error.response?.data?.detail || error.message}`);
        }
    }
}

// Example 1: Content Generation
async function exampleContentGeneration() {
    console.log('\n=== Content Generation Examples ===');
    
    const client = new GrowerAIClient();
    
    try {
        // Marketing content
        console.log('\n1. Marketing Content Generation:');
        const marketingResult = await client.generateContent(
            "Create a compelling social media post about the launch of an innovative AI-powered fitness app",
            {
                model: 'azure/grok-3',
                useCase: 'creative_writing',
                temperature: 0.8,
                maxTokens: 300
            }
        );
        console.log(`Generated content: ${marketingResult.result.substring(0, 200)}...`);
        console.log(`Model used: ${marketingResult.metadata.model_used}`);
        console.log(`Response time: ${marketingResult.metadata.response_time.toFixed(2)}s`);
        
        // Technical content
        console.log('\n2. Technical Documentation:');
        const technicalResult = await client.generateContent(
            "Explain the advantages of using Docker containers in modern software development",
            {
                model: 'azure/deepseek',
                useCase: 'technical_analysis',
                temperature: 0.3,
                maxTokens: 600
            }
        );
        console.log(`Generated content: ${technicalResult.result.substring(0, 200)}...`);
        console.log(`Model used: ${technicalResult.metadata.model_used}`);
        
    } catch (error) {
        console.error('Content generation error:', error.message);
    }
}

// Example 2: Content Rewriting
async function exampleContentRewriting() {
    console.log('\n=== Content Rewriting Examples ===');
    
    const client = new GrowerAIClient();
    
    const originalContent = `
        Our app is really good and helps people track their workouts. 
        It has lots of features and users seem to like it. 
        We think it's better than other fitness apps.
    `;
    
    try {
        // Professional rewrite
        console.log('\n1. Professional Style:');
        const professionalResult = await client.rewriteContent(originalContent, {
            style: 'professional',
            tone: 'authoritative',
            targetAudience: 'enterprise_clients'
        });
        console.log(`Original: ${originalContent.trim()}`);
        console.log(`Rewritten: ${professionalResult.rewritten_content}`);
        console.log(`Improvements: ${professionalResult.improvements.join(', ')}`);
        
        // Casual rewrite
        console.log('\n2. Casual Style:');
        const casualResult = await client.rewriteContent(originalContent, {
            style: 'casual',
            tone: 'friendly',
            targetAudience: 'general_users'
        });
        console.log(`Rewritten: ${casualResult.rewritten_content}`);
        
    } catch (error) {
        console.error('Content rewriting error:', error.message);
    }
}

// Example 3: Model Selection
async function exampleModelSelection() {
    console.log('\n=== Model Selection Examples ===');
    
    const client = new GrowerAIClient();
    
    const useCases = [
        'creative_writing',
        'technical_analysis',
        'content_rewriting',
        'simple_tasks',
        'code_generation'
    ];
    
    for (const useCase of useCases) {
        try {
            const recommendation = await client.getModelRecommendation(useCase);
            console.log(`\nUse case: ${useCase}`);
            console.log(`Recommended model: ${recommendation.recommended_model}`);
            console.log(`Reasoning: ${recommendation.model_info.reasoning || 'N/A'}`);
        } catch (error) {
            console.error(`Error getting recommendation for ${useCase}:`, error.message);
        }
    }
}

// Example 4: Error Handling with Retries
async function exampleErrorHandling() {
    console.log('\n=== Error Handling Examples ===');
    
    const client = new GrowerAIClient();
    
    async function robustApiCall(prompt, maxRetries = 3) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                return await client.generateContent(prompt);
            } catch (error) {
                if (error.response?.status === 429) {  // Rate limit
                    const waitTime = Math.pow(2, attempt) * 1000;
                    console.log(`Rate limited. Waiting ${waitTime}ms before retry...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    continue;
                }
                
                if (error.response?.status >= 500) {  // Server error
                    console.log(`Server error: ${error.response.status}. Retrying...`);
                    continue;
                }
                
                throw error;  // Client error, don't retry
            }
        }
        
        throw new Error(`Failed after ${maxRetries} attempts`);
    }
    
    try {
        const result = await robustApiCall("Explain the concept of machine learning in simple terms");
        console.log(`Success: ${result.result.substring(0, 100)}...`);
    } catch (error) {
        console.error('All retries failed:', error.message);
    }
}

// Example 5: Batch Processing
async function exampleBatchProcessing() {
    console.log('\n=== Batch Processing Example ===');
    
    const client = new GrowerAIClient();
    
    const prompts = [
        "Explain artificial intelligence in one paragraph",
        "Describe the benefits of cloud computing",
        "What is machine learning?",
        "Define data science briefly",
        "Explain blockchain technology"
    ];
    
    console.log(`Processing ${prompts.length} prompts...`);
    const startTime = Date.now();
    
    // Process with concurrency limit
    const concurrencyLimit = 3;
    const results = [];
    
    for (let i = 0; i < prompts.length; i += concurrencyLimit) {
        const batch = prompts.slice(i, i + concurrencyLimit);
        const batchPromises = batch.map(async (prompt, index) => {
            try {
                const result = await client.generateContent(prompt, {
                    model: 'azure/gpt-35-turbo',  // Fast model for batch processing
                    maxTokens: 200,
                    temperature: 0.5
                });
                console.log(`Completed ${i + index + 1}/${prompts.length}`);
                return result;
            } catch (error) {
                console.error(`Failed prompt ${i + index + 1}:`, error.message);
                return null;
            }
        });
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches to avoid rate limits
        if (i + concurrencyLimit < prompts.length) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    
    const totalTime = Date.now() - startTime;
    const successfulResults = results.filter(r => r !== null);
    
    console.log(`\nBatch Processing Summary:`);
    console.log(`Total time: ${(totalTime / 1000).toFixed(2)}s`);
    console.log(`Successful requests: ${successfulResults.length}/${prompts.length}`);
    console.log(`Average time per request: ${(totalTime / prompts.length / 1000).toFixed(2)}s`);
    
    // Show sample results
    successfulResults.slice(0, 3).forEach((result, index) => {
        if (result) {
            console.log(`\nSample result ${index + 1}: ${result.result.substring(0, 100)}...`);
        }
    });
}

// Example 6: Model Comparison
async function exampleModelComparison() {
    console.log('\n=== Model Comparison Example ===');
    
    const client = new GrowerAIClient();
    
    try {
        const comparison = await client.compareModels(
            "Explain the benefits of renewable energy for businesses",
            ['azure/gpt-4o', 'azure/gpt-4.1', 'azure/deepseek'],
            ['accuracy', 'clarity', 'business_relevance']
        );
        
        console.log(`\nComparison Results:`);
        console.log(`Winner: ${comparison.winner}`);
        console.log(`Summary: ${comparison.summary}`);
        
        comparison.comparison_results.forEach((result, index) => {
            console.log(`\nModel ${index + 1}: ${result.model}`);
            console.log(`Overall Score: ${result.overall_score}`);
            console.log(`Cost: $${result.cost.toFixed(4)}`);
            console.log(`Response Time: ${result.response_time.toFixed(2)}s`);
        });
        
        console.log(`\nCost Analysis:`);
        console.log(`Most cost-effective: ${comparison.cost_analysis.most_cost_effective}`);
        console.log(`Best value: ${comparison.cost_analysis.best_value}`);
        
    } catch (error) {
        console.error('Model comparison error:', error.message);
    }
}

// Main function to run all examples
async function main() {
    console.log('Enhanced FastAPI - JavaScript Usage Examples');
    console.log('='.repeat(50));
    
    try {
        // Test basic connectivity first
        const client = new GrowerAIClient();
        const health = await client.healthCheck();
        console.log('✅ API is healthy and accessible');
        console.log(`API Status: ${health.status}`);
        console.log(`Available Models: ${health.available_models}`);
        
        // Run examples
        await exampleContentGeneration();
        await exampleContentRewriting();
        await exampleModelSelection();
        await exampleErrorHandling();
        await exampleBatchProcessing();
        await exampleModelComparison();
        
        console.log('\n' + '='.repeat(50));
        console.log('✅ All examples completed successfully!');
        
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.error('❌ Cannot connect to API. Make sure the server is running on http://localhost:8000');
        } else {
            console.error('❌ Error running examples:', error.message);
        }
    }
}

// Export for use as module
module.exports = {
    GrowerAIClient,
    exampleContentGeneration,
    exampleContentRewriting,
    exampleModelSelection,
    exampleErrorHandling,
    exampleBatchProcessing,
    exampleModelComparison
};

// Run examples if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
