import time
import logging
from litellm import completion
import os
import json
import re
from post_processing_layer.post_proc import *
from Model_call.model_manager import get_model_manager
from dotenv import load_dotenv

# Load environment variables from the .env file
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Get the global model manager instance
model_manager = get_model_manager()

# Legacy environment variable setup (maintained for backward compatibility)
# set AWS ENV variables
os.environ["AWS_ACCESS_KEY_ID"] = os.getenv("litellm_access_key")
os.environ["AWS_SECRET_ACCESS_KEY"] = os.getenv("litellm_secret_key")
os.environ["AWS_REGION_NAME"] = os.getenv("AWS_REGION_NAME")

## set Azure OpenAI ENV variables
os.environ["AZURE_API_KEY"] = os.getenv("AZURE_API_KEY")
os.environ["AZURE_API_BASE"] = os.getenv("AZURE_API_BASE")
os.environ["AZURE_API_VERSION"] = os.getenv("AZURE_API_VERSION")



def model_call_for_short_content_feature(prompts_instruction, model_name=None):
    """
    Invoke a specified language model to process a given prompt and return the formatted response.
    Enhanced with multi-endpoint Azure OpenAI support via ModelManager.

    Args:
        prompts_instruction (str): The instruction or input prompt to be sent to the language model.
        model_name (str, optional): The name of the model to be used. Supports new Azure models:
            - azure/gpt-4.1 (East US 2)
            - azure/o1 (East US 2)
            - azure/gpt-4o (West Europe)
            - azure/grok-3 (West Europe)
            - azure/llama-3.3 (West Europe)
            - azure/deepseek (West Europe)
            - Legacy models: gpt-4o-mini, gpt-35-turbo, gpt-4o

    Returns:
        str: The formatted response from the model, with specific phrases adjusted according to the processing rules.

    Raises:
        Exception: If all model calls fail.

    Notes:
        - Uses ModelManager for intelligent model selection and fallback
        - Automatically routes to appropriate Azure endpoints
        - Text processing removes predefined phrases such as "Here's a" from the response
    """

    try:
        # Use ModelManager for enhanced model calling with fallback
        logger.info(f"Calling model for short content: {model_name}")

        # Get the response using ModelManager
        result = model_manager.call_model(
            prompt=prompts_instruction,
            model_name=model_name,
            temperature=0.5,
            max_tokens=250
        )

        # Enhanced list of prefacing phrases to remove (including rewrite-specific phrases)
        prefacing_phrases = [
            "Here's a LinkedIn",
            "Here is a ",
            "Here's a LinkedIn post",
            "Here's a",
            "Here is",
            "Here's content",
            "Here's a social media post",
            "Here's a post",
            "Here's some content",
            "Here's your",
            "Below is a",
            "Below is your",
            "I'll create a",
            "I'll write a",
            "Here's a natural",
            "Here's a human-first",
            "Here's content written in",
            "Here's a refined version",
            "Here's the optimized content",
            "Here's the rewritten post",
            "Here's an improved version",
            "Here's the enhanced content",
            "Here's a better version",
            "Here's the refined post",
            "Here's your optimized",
            "Here's the updated content",
            "Here's a more engaging version"
        ]

        # Separators to remove (lines that are just separators)
        separator_patterns = ["---", "***", "===", "~~~", "```"]

        # Process text line by line
        processed_text = []
        for line in result.splitlines():
            line = line.strip()

            # Skip empty lines at the beginning
            if not line and not processed_text:
                continue

            # Skip separator lines
            if any(line.startswith(sep) or line == sep for sep in separator_patterns):
                continue

            # Remove prefacing phrases
            line_modified = False
            for phrase in prefacing_phrases:
                if line.lower().startswith(phrase.lower()):
                    # Remove everything before and including the first colon if present
                    if ":" in line:
                        line = ":".join(line.split(":", 1)[1:]).strip()
                    else:
                        # If no colon, skip this entire line as it's likely just a preface
                        line_modified = True
                        break
                    line_modified = True
                    break

            # Only add the line if it wasn't completely removed
            if not line_modified or line:
                processed_text.append(line)

        processed_result = "\n".join(processed_text)
        final_result = clean_and_complete_string(processed_result)

        logger.info("Short content generation completed successfully")
        return final_result

    except Exception as e:
        logger.error(f"Error in short content generation: {str(e)}")
        raise Exception(f"Short content generation failed: {str(e)}")



def model_call_for_long_content_feature(prompts_instruction, model_name=None):
    """
    Invoke a specified language model to process a given prompt and return the formatted response.
    Enhanced with multi-endpoint Azure OpenAI support via ModelManager.

    Args:
        prompts_instruction (str): The instruction or input prompt to be sent to the language model.
        model_name (str, optional): The name of the model to be used. Supports new Azure models:
            - azure/gpt-4.1 (East US 2) - Recommended for complex long content
            - azure/o1 (East US 2) - Advanced reasoning for complex content
            - azure/gpt-4o (West Europe) - Balanced performance
            - azure/grok-3 (West Europe) - Creative content generation
            - azure/llama-3.3 (West Europe) - Alternative model
            - azure/deepseek (West Europe) - Code and technical content

    Returns:
        str: The formatted response from the model, with specific phrases adjusted according to the processing rules.

    Raises:
        Exception: If all model calls fail.

    Notes:
        - Uses ModelManager for intelligent model selection and fallback
        - Automatically routes to appropriate Azure endpoints
        - Higher token limit (450) for long content generation
    """

    try:
        # Use ModelManager for enhanced model calling with fallback
        logger.info(f"Calling model for long content: {model_name}")

        # For long content, prefer more capable models if none specified
        if not model_name:
            model_name = model_manager.get_recommended_model("content_rewriting")

        # Get the response using ModelManager
        result = model_manager.call_model(
            prompt=prompts_instruction,
            model_name=model_name,
            temperature=0.5,
            max_tokens=450
        )

        # Enhanced list of prefacing phrases to remove (including rewrite-specific phrases)
        prefacing_phrases = [
            "Here's a LinkedIn",
            "Here is a ",
            "Here's a LinkedIn post",
            "Here's a",
            "Here is",
            "Here's content",
            "Here's a social media post",
            "Here's a post",
            "Here's some content",
            "Here's your",
            "Below is a",
            "Below is your",
            "I'll create a",
            "I'll write a",
            "Here's a natural",
            "Here's a human-first",
            "Here's content written in",
            "Here's a refined version",
            "Here's the optimized content",
            "Here's the rewritten post",
            "Here's an improved version",
            "Here's the enhanced content",
            "Here's a better version",
            "Here's the refined post",
            "Here's your optimized",
            "Here's the updated content",
            "Here's a more engaging version"
        ]

        # Separators to remove (lines that are just separators)
        separator_patterns = ["---", "***", "===", "~~~", "```"]

        # Process text line by line
        processed_text = []
        for line in result.splitlines():
            line = line.strip()

            # Skip empty lines at the beginning
            if not line and not processed_text:
                continue

            # Skip separator lines
            if any(line.startswith(sep) or line == sep for sep in separator_patterns):
                continue

            # Remove prefacing phrases
            line_modified = False
            for phrase in prefacing_phrases:
                if line.lower().startswith(phrase.lower()):
                    # Remove everything before and including the first colon if present
                    if ":" in line:
                        line = ":".join(line.split(":", 1)[1:]).strip()
                    else:
                        # If no colon, skip this entire line as it's likely just a preface
                        line_modified = True
                        break
                    line_modified = True
                    break

            # Only add the line if it wasn't completely removed
            if not line_modified or line:
                processed_text.append(line)

        processed_result = "\n".join(processed_text)
        final_result = clean_and_complete_string(processed_result)

        logger.info("Long content generation completed successfully")
        return final_result

    except Exception as e:
        logger.error(f"Error in long content generation: {str(e)}")
        raise Exception(f"Long content generation failed: {str(e)}")



# Model invocation using the LiteLLM library with enhanced multi-endpoint support
def model_call_for_on_the_go_feature(prompts_instruction, model_name=None):
    """
    Invoke a specified language model to process a given prompt and return the formatted response.
    Enhanced with multi-endpoint Azure OpenAI support via ModelManager.

    Args:
        prompts_instruction (str): The instruction or input prompt to be sent to the language model.
        model_name (str, optional): The name of the model to be used. Supports new Azure models:
            - azure/gpt-4o-mini (Budget-friendly for simple on-the-go content)
            - azure/gpt-4o (Balanced performance)
            - azure/grok-3 (Creative quick content)
            - azure/llama-3.3 (Alternative model)

    Returns:
        str: The formatted response from the model, with specific phrases adjusted according to the processing rules.

    Raises:
        Exception: If all model calls fail.

    Notes:
        - Uses ModelManager for intelligent model selection and fallback
        - Optimized for quick, on-the-go content generation
        - Lower token limit (250) for concise responses
    """

    try:
        # Use ModelManager for enhanced model calling with fallback
        logger.info(f"Calling model for on-the-go content: {model_name}")

        # For on-the-go content, prefer faster, cost-effective models if none specified
        if not model_name:
            model_name = model_manager.get_recommended_model("simple_tasks")

        # Get the response using ModelManager
        result = model_manager.call_model(
            prompt=prompts_instruction,
            model_name=model_name,
            temperature=0.5,
            max_tokens=250
        )

        # List of phrases to check for
        phrases = [
            "Here's a LinkedIn",
            "Here is a ",
            "Here's a LinkedIn post",
            "Here's a",
            "Here is"
        ]

        # Process text line by line
        processed_text = []
        for line in result.splitlines():
            if any(line.startswith(phrase) for phrase in phrases):
                line = ":".join(line.split(":", 1)[1:]).strip()  # Remove part before first colon
            processed_text.append(line)

        processed_result = "\n".join(processed_text)
        final_result = clean_and_complete_string(processed_result)

        logger.info("On-the-go content generation completed successfully")
        return final_result

    except Exception as e:
        logger.error(f"Error in on-the-go content generation: {str(e)}")
        raise Exception(f"On-the-go content generation failed: {str(e)}")


def model_call_for_image_generation(prompt_instruction, model_name="dall-e-3", num_images=1):
    """
    Invoke the Azure OpenAI DALL·E model to generate an image based on a given prompt.

    DEPRECATED: This function is deprecated. Use ModelManager.call_image_model() instead
    for better Helicone integration and enhanced features.

    Args:
        prompt_instruction (str): The instruction or prompt to generate the image.
        model_name (str, optional): The name of the model to be used. Defaults to "dall-e-3".
        num_images (int, optional): The number of images to generate. Defaults to 1.

    Returns:
        str: The URL of the generated image.

    Raises:
        Exception: If an error occurs during the image generation process.
    """

    logger.warning("model_call_for_image_generation is deprecated. Use ModelManager.call_image_model() instead.")

    try:
        # Use ModelManager for enhanced Helicone integration
        azure_model_name = f"azure/{model_name}" if not model_name.startswith("azure/") else model_name

        result = model_manager.call_image_model(
            prompt=prompt_instruction,
            model_name=azure_model_name,
            n=num_images
        )

        image_url = result.get("image_url")
        print("Image URL:", image_url)

        # Display the image inline (if in a Jupyter notebook)
        try:
            from IPython.display import Image, display
            display(Image(url=image_url))
        except ImportError:
            # IPython not available, skip display
            pass

        return image_url

    except Exception as e:
        print(f"Error generating image: {str(e)}")
        raise Exception(f"Error generating image: {str(e)}")


# New enhanced model calling functions leveraging the new Azure models

def model_call_with_specific_model(prompts_instruction, model_name, temperature=0.5, max_tokens=250, system_prompt=None):
    """
    Call a specific model with enhanced error handling and logging.

    Args:
        prompts_instruction (str): The user prompt
        model_name (str): Specific model to use (e.g., "azure/gpt-4.1", "azure/deepseek")
        temperature (float): Sampling temperature
        max_tokens (int): Maximum tokens to generate
        system_prompt (str, optional): System prompt for context

    Returns:
        str: Generated content
    """
    try:
        logger.info(f"Calling specific model: {model_name}")

        result = model_manager.call_model(
            prompt=prompts_instruction,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            system_prompt=system_prompt
        )

        logger.info(f"Successfully generated content with {model_name}")
        return result

    except Exception as e:
        logger.error(f"Error calling model {model_name}: {str(e)}")
        raise Exception(f"Model call failed for {model_name}: {str(e)}")


def model_call_with_fallback_chain(prompts_instruction, preferred_models, temperature=0.5, max_tokens=250):
    """
    Call models in a specific order with fallback.

    Args:
        prompts_instruction (str): The user prompt
        preferred_models (List[str]): List of models to try in order
        temperature (float): Sampling temperature
        max_tokens (int): Maximum tokens to generate

    Returns:
        Dict: Result with content and model used
    """
    for model_name in preferred_models:
        try:
            logger.info(f"Trying model: {model_name}")

            result = model_manager.call_model(
                prompt=prompts_instruction,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens
            )

            return {
                "content": result,
                "model_used": model_name,
                "success": True
            }

        except Exception as e:
            logger.warning(f"Model {model_name} failed: {str(e)}")
            continue

    raise Exception("All preferred models failed")


def model_call_for_creative_content(prompts_instruction, creativity_level="medium"):
    """
    Specialized function for creative content generation.

    Args:
        prompts_instruction (str): The creative prompt
        creativity_level (str): "low", "medium", "high"

    Returns:
        str: Generated creative content
    """
    # Select model and temperature based on creativity level
    if creativity_level == "high":
        model_name = "azure/grok-3"  # Grok is known for creativity
        temperature = 0.8
    elif creativity_level == "medium":
        model_name = "azure/gpt-4o"
        temperature = 0.6
    else:  # low creativity
        model_name = "azure/gpt-4o-mini"
        temperature = 0.3

    try:
        logger.info(f"Generating creative content with {model_name} (creativity: {creativity_level})")

        result = model_manager.call_model(
            prompt=prompts_instruction,
            model_name=model_name,
            temperature=temperature,
            max_tokens=400,
            system_prompt="You are a creative content generator. Focus on originality, engagement, and unique perspectives."
        )

        return clean_and_complete_string(result)

    except Exception as e:
        logger.error(f"Error in creative content generation: {str(e)}")
        raise Exception(f"Creative content generation failed: {str(e)}")


def model_call_for_technical_content(prompts_instruction, complexity="medium"):
    """
    Specialized function for technical content generation.

    Args:
        prompts_instruction (str): The technical prompt
        complexity (str): "simple", "medium", "complex"

    Returns:
        str: Generated technical content
    """
    # Select model based on complexity
    if complexity == "complex":
        model_name = "azure/gpt-4.1"  # Most capable model for complex technical content
    elif complexity == "medium":
        model_name = "azure/gpt-4o"
    else:  # simple
        model_name = "azure/gpt-4o-mini"

    try:
        logger.info(f"Generating technical content with {model_name} (complexity: {complexity})")

        result = model_manager.call_model(
            prompt=prompts_instruction,
            model_name=model_name,
            temperature=0.3,  # Lower temperature for technical accuracy
            max_tokens=500,
            system_prompt="You are a technical content expert. Focus on accuracy, clarity, and comprehensive explanations."
        )

        return clean_and_complete_string(result)

    except Exception as e:
        logger.error(f"Error in technical content generation: {str(e)}")
        raise Exception(f"Technical content generation failed: {str(e)}")


# Utility functions for model management

def get_available_models():
    """Get list of all available models."""
    return model_manager.get_available_models()


def get_model_info(model_name):
    """Get detailed information about a specific model."""
    return model_manager.get_model_info(model_name)


def get_recommended_model_for_use_case(use_case):
    """Get recommended model for a specific use case."""
    return model_manager.get_recommended_model(use_case)


def check_endpoint_health():
    """Check health status of all endpoints."""
    return model_manager.get_endpoint_health()
