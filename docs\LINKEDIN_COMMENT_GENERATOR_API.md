# LinkedIn Comment Generator API Documentation

## Overview

The LinkedIn Comment Generator module creates authentic, human-like LinkedIn comments based on post content and comment type. It features automatic language detection (English, Hindi, Hinglish), type-specific comment templates, and natural conversation patterns that bypass AI detection.

## Features

- **5 Comment Types**: Positive, constructive, question, personal_experience, insight
- **Automatic Language Detection**: Supports English, Hindi, Hinglish, and other languages
- **Authentic Human Voice**: Bypasses AI detection with natural, conversational patterns
- **Concise Output**: Optimized for LinkedIn's 40-50 word comment format
- **Type-Specific Templates**: Tailored responses for each comment type
- **Clean Output**: Ready-to-post content without prefacing text or formatting

## API Endpoint

### POST /process

Generate authentic LinkedIn comments based on post content and desired comment type.

**Request Body:**
```json
{
    "data": {
        "post_content": "LinkedIn post content to comment on",
        "comment_type": "positive",
        "model_name": "azure/gpt-4o"
    },
    "operation": "comment_generation"
}
```

## Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `post_content` | string | The original LinkedIn post content to comment on |
| `comment_type` | string | Type of comment to generate (see Comment Types below) |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `model_name` | string | "azure/gpt-4o" | Azure OpenAI model to use for generation |

## Comment Types

### 1. Positive
**Purpose**: Supportive and encouraging response  
**Tone**: Express genuine appreciation, agreement, or encouragement  
**Examples**:
- English: "Great insights!", "This resonates with me!", "Thanks for sharing this perspective!"
- Hinglish: "Bilkul sahi kaha!", "Great point yaar!", "This is so relatable!"
- Hindi: "बहुत बढ़िया विचार!", "सही कहा आपने!", "अच्छी जानकारी है!"

### 2. Constructive
**Purpose**: Thoughtful feedback or additional perspective  
**Tone**: Provide respectful, thoughtful feedback or add a different perspective  
**Examples**:
- English: "I'd add that...", "Another perspective could be...", "Have you considered..."
- Hinglish: "One more point add karna chahunga...", "Ek aur angle se dekhen to..."
- Hindi: "एक और बात जोड़ना चाहूंगा...", "दूसरे नजरिए से देखें तो..."

### 3. Question
**Purpose**: Curious inquiry to engage further  
**Tone**: Ask genuine, thoughtful questions that show interest  
**Examples**:
- English: "How did you discover this?", "What's your experience with...?"
- Hinglish: "Yeh kaise pata chala aapko?", "Aapka experience kya hai is baare mein?"
- Hindi: "यह कैसे पता चला आपको?", "इस बारे में आपका अनुभव क्या है?"

### 4. Personal Experience
**Purpose**: Relating personal experience to the topic  
**Tone**: Share a brief, relevant personal experience that connects to the post  
**Examples**:
- English: "I experienced something similar...", "This reminds me of when..."
- Hinglish: "Mere saath bhi aisa hua hai...", "This reminds me of my experience..."
- Hindi: "मेरे साथ भी ऐसा हुआ है...", "यह मुझे अपने अनुभव की याद दिलाता है..."

### 5. Insight
**Purpose**: Adding valuable expertise or knowledge  
**Tone**: Share professional insights, expertise, or valuable information  
**Examples**:
- English: "From my experience in...", "Research shows that...", "Industry best practice suggests..."
- Hinglish: "Mere field mein experience se...", "Studies show karte hain ki..."
- Hindi: "मेरे क्षेत्र के अनुभव से...", "अनुसंधान दिखाता है कि..."

## Language Detection

The system automatically detects the language of the input post content and generates comments in the same language:

- **English**: Clear, natural English comments
- **Hinglish**: Natural mix of Hindi and English using Roman script
- **Hindi**: Comments in Hindi using Devanagari script
- **Other Languages**: Matches the language style and tone of the original post

## Response Format

### Success Response
```json
{
    "result": "Great insights! This really resonates with my experience in the industry.",
    "status": "success",
    "message": "Operation 'comment_generation' completed successfully"
}
```

### Error Response
```json
{
    "detail": "Invalid comment type. Must be one of: positive, constructive, question, personal_experience, insight",
    "status_code": 400
}
```

## Usage Examples

### Basic Positive Comment
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "post_content": "Just launched our new AI-powered analytics platform! Excited to see how it helps businesses make data-driven decisions.",
      "comment_type": "positive"
    },
    "operation": "comment_generation"
  }'
```

### Constructive Feedback Comment
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "post_content": "Remote work has completely changed how we collaborate. Teams are more productive than ever.",
      "comment_type": "constructive",
      "model_name": "azure/gpt-4o"
    },
    "operation": "comment_generation"
  }'
```

### Question Comment
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "post_content": "The future of sustainable technology lies in renewable energy integration with smart grid systems.",
      "comment_type": "question"
    },
    "operation": "comment_generation"
  }'
```

### Personal Experience Comment
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "post_content": "Leadership is about empowering others to achieve their potential, not just managing tasks.",
      "comment_type": "personal_experience"
    },
    "operation": "comment_generation"
  }'
```

### Insight Comment with Specific Model
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "post_content": "Machine learning is transforming healthcare diagnostics with unprecedented accuracy rates.",
      "comment_type": "insight",
      "model_name": "azure/grok-3"
    },
    "operation": "comment_generation"
  }'
```

### Hinglish Post Example
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "post_content": "Startup journey mein sabse important cheez hai persistence. Failures se seekhna aur aage badhna.",
      "comment_type": "personal_experience"
    },
    "operation": "comment_generation"
  }'
```

## Error Handling

### Common Errors

| Error | Description | Solution |
|-------|-------------|----------|
| Missing required field: 'post_content' | Post content parameter not provided | Include post content in request |
| Missing required field: 'comment_type' | Comment type parameter not provided | Include valid comment type |
| Invalid comment type | Comment type not in supported list | Use one of: positive, constructive, question, personal_experience, insight |
| Empty post content | Post content is empty or whitespace only | Provide meaningful post content |
| Model unavailable | Specified model is not available | Use default model or check available models |

### Best Practices

1. **Post Content**: Provide meaningful, substantial post content for better comment generation
2. **Comment Type Selection**: Choose the comment type that best fits your engagement strategy
3. **Language Consistency**: The system will automatically match the language of the original post
4. **Length Optimization**: Comments are automatically optimized for LinkedIn's format (40-50 words)
5. **Model Selection**: Use default "azure/gpt-4o" for best results, or specify alternative models as needed

## Integration Notes

- **Consistent API Pattern**: Follows the same request/response format as other content generation modules
- **Model Manager Integration**: Uses the unified model management system with automatic fallback
- **Helicone Logging**: Automatic API monitoring and analytics for all requests
- **Clean Output Standards**: Implements the same post-processing as other content modules
- **Production Ready**: No test functions or hardcoded content in the production module

## Testing

Use the provided test scripts and Postman collection for comprehensive testing:

```bash
# Run module tests
python tests/test_comment_generator.py

# Import Postman collection
# File: postman/comment_generator_collection.json
```

## Complete cURL Example

Here's a complete example demonstrating the LinkedIn comment generation API:

```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "data": {
      "post_content": "Just completed a fascinating project on AI ethics in healthcare. The intersection of technology and human values is more critical than ever. We need frameworks that ensure AI serves humanity while maintaining privacy and trust.",
      "comment_type": "insight",
      "model_name": "azure/gpt-4o"
    },
    "operation": "comment_generation"
  }'
```

**Expected Response:**
```json
{
    "result": "From my experience in healthcare tech, establishing clear ethical guidelines early in development is crucial. Have you explored any specific frameworks for balancing innovation with patient privacy?",
    "status": "success",
    "message": "Operation 'comment_generation' completed successfully"
}
```

This example demonstrates:
- Professional insight-type comment
- Natural question to encourage engagement
- Relevant expertise sharing
- Appropriate length for LinkedIn
- Human-like conversational tone
