#!/bin/bash

# Enhanced FastAPI - cURL Usage Examples
# Demonstrates common use cases and best practices using cURL commands

# Configuration
BASE_URL="http://localhost:8000"
HELICONE_API_KEY="your_helicone_api_key_here"  # Replace with actual key

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper function to print section headers
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Helper function to print success messages
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Helper function to print error messages
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Helper function to print info messages
print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Function to check API health
check_api_health() {
    print_header "API Health Check"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json \
        -X GET "$BASE_URL/health" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY")
    
    http_code="${response: -3}"
    
    if [ "$http_code" -eq 200 ]; then
        print_success "API is healthy and accessible"
        status=$(cat /tmp/health_response.json | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        available_models=$(cat /tmp/health_response.json | grep -o '"available_models":[0-9]*' | cut -d':' -f2)
        echo "Status: $status"
        echo "Available Models: $available_models"
        return 0
    else
        print_error "API health check failed (HTTP $http_code)"
        cat /tmp/health_response.json 2>/dev/null
        return 1
    fi
}

# Example 1: Basic Content Generation
example_content_generation() {
    print_header "Content Generation Examples"
    
    # Marketing content
    print_info "1. Marketing Content Generation"
    curl -s -X POST "$BASE_URL/process" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -H "Helicone-Property-Use-Case: creative_writing" \
        -H "Helicone-Property-Content-Type: marketing" \
        -d '{
            "data": "Create an engaging product announcement for a revolutionary AI-powered smart home security system",
            "operation": "content_generation",
            "parameters": {
                "model": "azure/grok-3",
                "max_tokens": 500,
                "temperature": 0.8,
                "use_case": "creative_writing"
            }
        }' | jq -r '.result' | head -c 200
    echo "..."
    print_success "Marketing content generated"
    
    # Technical documentation
    print_info "2. Technical Documentation"
    curl -s -X POST "$BASE_URL/process" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -H "Helicone-Property-Use-Case: technical_analysis" \
        -d '{
            "data": "Explain the benefits of implementing CI/CD pipelines in software development",
            "operation": "technical_analysis",
            "parameters": {
                "model": "azure/deepseek",
                "max_tokens": 600,
                "temperature": 0.3,
                "use_case": "technical_analysis"
            }
        }' | jq -r '.result' | head -c 200
    echo "..."
    print_success "Technical documentation generated"
    
    # Creative writing
    print_info "3. Creative Writing"
    curl -s -X POST "$BASE_URL/process" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -H "Helicone-Property-Use-Case: creative_writing" \
        -d '{
            "data": "Write a short story about a programmer who discovers their code can predict the future",
            "operation": "creative_writing",
            "parameters": {
                "model": "azure/grok-3",
                "max_tokens": 800,
                "temperature": 0.9,
                "use_case": "creative_writing"
            }
        }' | jq -r '.result' | head -c 200
    echo "..."
    print_success "Creative content generated"
}

# Example 2: Content Repurposing
example_content_rewriting() {
    print_header "Content Repurposing Examples"
    
    original_content="Our software update has new features. It works faster and is more secure. Users will find the interface improved and easier to navigate."
    
    # Professional content repurposing
    print_info "1. Professional Content Repurposing"
    echo "Original: $original_content"
    echo ""
    echo "Repurposed:"
    curl -s -X POST "$BASE_URL/process" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -H "Helicone-Property-Use-Case: content_repurposing" \
        -d "{
            \"data\": {
                \"content\": \"$original_content\",
                \"tone\": \"professional\",
                \"target_audience\": \"enterprise_executives\",
                \"model_name\": \"azure/gpt-4o\"
            },
            \"operation\": \"repurpose_content\"
        }" | jq -r '.result[0].text'
    print_success "Professional repurposing completed"

    # Engaging content repurposing
    print_info "2. Engaging Content Repurposing"
    curl -s -X POST "$BASE_URL/process" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -d "{
            \"data\": {
                \"content\": \"$original_content\",
                \"tone\": \"engaging\",
                \"target_audience\": \"general_users\",
                \"model_name\": \"azure/gpt-4o\"
            },
            \"operation\": \"repurpose_content\"
        }" | jq -r '.result[0].text'
    print_success "Engaging repurposing completed"
}

# Example 3: Model Recommendations
example_model_recommendations() {
    print_header "Model Recommendation Examples"
    
    use_cases=("creative_writing" "technical_analysis" "content_rewriting" "simple_tasks" "code_generation")
    
    for use_case in "${use_cases[@]}"; do
        print_info "Use case: $use_case"
        response=$(curl -s -X GET "$BASE_URL/models/recommended/$use_case" \
            -H "Helicone-Auth: Bearer $HELICONE_API_KEY")
        
        recommended_model=$(echo "$response" | jq -r '.recommended_model')
        reasoning=$(echo "$response" | jq -r '.model_info.reasoning // "N/A"')
        
        echo "Recommended model: $recommended_model"
        echo "Reasoning: $reasoning"
        echo ""
    done
    
    print_success "Model recommendations retrieved"
}

# Example 4: Model Comparison
example_model_comparison() {
    print_header "Model Comparison Example"
    
    print_info "Comparing models for business content generation"
    
    curl -s -X POST "$BASE_URL/models/compare" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -d '{
            "prompt": "Explain the benefits of digital transformation for small businesses",
            "models": ["azure/gpt-4o", "azure/gpt-4.1", "azure/deepseek"],
            "evaluation_criteria": ["clarity", "accuracy", "business_relevance"],
            "include_cost_analysis": true
        }' > /tmp/comparison_result.json
    
    winner=$(cat /tmp/comparison_result.json | jq -r '.winner')
    summary=$(cat /tmp/comparison_result.json | jq -r '.summary')
    most_cost_effective=$(cat /tmp/comparison_result.json | jq -r '.cost_analysis.most_cost_effective')
    
    echo "Winner: $winner"
    echo "Summary: $summary"
    echo "Most cost-effective: $most_cost_effective"
    
    print_success "Model comparison completed"
}

# Example 5: Advanced Content Repurposing
example_advanced_repurposing() {
    print_header "Advanced Content Repurposing Example"

    print_info "Repurposing content for different audiences"

    # Business professionals
    print_info "Repurposing for business professionals"
    curl -s -X POST "$BASE_URL/process" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -H "Helicone-Property-Use-Case: content_repurposing" \
        -d '{
            "data": {
                "content": "Join our webinar to learn about the latest trends in artificial intelligence and machine learning",
                "tone": "professional",
                "target_audience": "business_professionals",
                "model_name": "azure/gpt-4.1"
            },
            "operation": "repurpose_content"
        }' > /tmp/business_repurpose.json
    
    recommended_variant=$(cat /tmp/advanced_rewrite.json | jq -r '.recommended_variant')
    total_variants=$(cat /tmp/advanced_rewrite.json | jq -r '.metadata.total_variants')
    
    echo "Generated $total_variants variants"
    echo "Recommended variant: $recommended_variant"
    
    # Show the recommended variant
    echo ""
    echo "Recommended content:"
    cat /tmp/advanced_rewrite.json | jq -r ".variants[$recommended_variant].content"
    
    print_success "Advanced rewriting completed"
}

# Example 6: Monitoring and Analytics
example_monitoring() {
    print_header "Monitoring and Analytics Examples"
    
    # Get monitoring summary
    print_info "1. Monitoring Summary"
    curl -s -X GET "$BASE_URL/monitoring/summary" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" > /tmp/monitoring.json
    
    total_requests=$(cat /tmp/monitoring.json | jq -r '.helicone_data.total_requests // 0')
    total_cost=$(cat /tmp/monitoring.json | jq -r '.helicone_data.total_cost // 0')
    cache_hit_rate=$(cat /tmp/monitoring.json | jq -r '.helicone_data.cache_hit_rate // 0')
    
    echo "Total requests: $total_requests"
    echo "Total cost: \$$total_cost"
    echo "Cache hit rate: $(echo "$cache_hit_rate * 100" | bc -l | cut -c1-4)%"
    
    # Get performance comparison
    print_info "2. Performance Analytics"
    curl -s -X GET "$BASE_URL/analytics/performance-comparison?time_range=24h&include_costs=true" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" > /tmp/performance.json
    
    echo "Performance data retrieved for last 24 hours"
    
    # Manual health check
    print_info "3. Manual Health Check"
    curl -s -X POST "$BASE_URL/monitoring/health-check" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" > /tmp/health_check.json
    
    message=$(cat /tmp/health_check.json | jq -r '.message')
    echo "$message"
    
    print_success "Monitoring examples completed"
}

# Example 7: Configuration Validation
example_configuration() {
    print_header "Configuration Validation Examples"
    
    # Validate configuration
    print_info "1. Configuration Validation"
    curl -s -X GET "$BASE_URL/config/validate" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" > /tmp/config.json
    
    config_valid=$(cat /tmp/config.json | jq -r '.configuration_valid')
    total_configured=$(cat /tmp/config.json | jq -r '.model_configurations.total_configured')
    valid_configurations=$(cat /tmp/config.json | jq -r '.model_configurations.valid_configurations')
    
    echo "Configuration valid: $config_valid"
    echo "Models configured: $valid_configurations/$total_configured"
    
    # Test specific model
    print_info "2. Test Model Configuration"
    curl -s -X POST "$BASE_URL/config/test-model" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -d '{
            "model": "azure/gpt-4o",
            "test_prompt": "Hello, world! Please respond with a brief greeting.",
            "include_performance_test": true
        }' > /tmp/model_test.json
    
    test_successful=$(cat /tmp/model_test.json | jq -r '.test_successful')
    response_time=$(cat /tmp/model_test.json | jq -r '.performance_metrics.response_time')
    cost=$(cat /tmp/model_test.json | jq -r '.performance_metrics.cost')
    
    echo "Test successful: $test_successful"
    echo "Response time: ${response_time}s"
    echo "Cost: \$$cost"
    
    print_success "Configuration validation completed"
}

# Example 8: Error Handling and Retries
example_error_handling() {
    print_header "Error Handling Example"
    
    print_info "Testing with invalid model (should fail gracefully)"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/error_response.json \
        -X POST "$BASE_URL/process" \
        -H "Content-Type: application/json" \
        -H "Helicone-Auth: Bearer $HELICONE_API_KEY" \
        -d '{
            "data": "Test prompt",
            "operation": "content_generation",
            "parameters": {
                "model": "azure/invalid-model",
                "max_tokens": 100
            }
        }')
    
    http_code="${response: -3}"
    
    if [ "$http_code" -ne 200 ]; then
        echo "Expected error received (HTTP $http_code):"
        cat /tmp/error_response.json | jq -r '.detail'
        
        # Show error code if available
        error_code=$(cat /tmp/error_response.json | jq -r '.error_code // "N/A"')
        echo "Error code: $error_code"
        
        print_success "Error handling working correctly"
    else
        print_error "Expected error but got success"
    fi
}

# Main function
main() {
    echo -e "${BLUE}Enhanced FastAPI - cURL Usage Examples${NC}"
    echo "=================================================="
    
    # Check if jq is installed
    if ! command -v jq &> /dev/null; then
        print_error "jq is required for JSON parsing. Please install it first."
        echo "Ubuntu/Debian: sudo apt-get install jq"
        echo "macOS: brew install jq"
        exit 1
    fi
    
    # Check API health first
    if ! check_api_health; then
        print_error "API is not accessible. Please ensure the server is running."
        exit 1
    fi
    
    # Run all examples
    example_content_generation
    example_content_rewriting
    example_model_recommendations
    example_model_comparison
    example_advanced_repurposing
    example_monitoring
    example_configuration
    example_error_handling
    
    echo ""
    echo "=================================================="
    print_success "All cURL examples completed successfully!"
    
    # Cleanup temporary files
    rm -f /tmp/health_response.json /tmp/comparison_result.json /tmp/advanced_rewrite.json
    rm -f /tmp/monitoring.json /tmp/performance.json /tmp/health_check.json
    rm -f /tmp/config.json /tmp/model_test.json /tmp/error_response.json
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
