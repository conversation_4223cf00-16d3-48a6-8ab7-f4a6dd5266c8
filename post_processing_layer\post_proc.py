# import re

# # Function to clean and adjust the string
# def clean_and_complete_string(text):
#     """
#     Post processing of the reponse layer
#     """

#     words_with_suffix = [
#     "Attention:", "Interest:", "Desire:", "Action:", "Problem:", "Agitation:", 
#     "Solution:", "Stop:", "Look:", "Act:", "Purchase:", "Before:", "After:", 
#     "Bridge:", "Clear:", "Concise:", "Compelling:", "Credible:", "Features:", 
#     "Advantages:", "Benefits:", "Feature:", "Advantage:", "Benefit:"
#     ]
    
#     # Step 0: Post-Processing of text - Removing words with suffix ":" (case insensitive)
#     text = text.replace("|", "")  # Remove "|" from text

#     for word in words_with_suffix:
#         input_string = text.replace(word, "")  # Original case
#         input_string = text.replace(word.lower(), "")  # Lowercase
#         input_string = text.replace(word.upper(), "")  # Uppercase
#         input_string = text.replace(word.capitalize(), "")  # Capitalized case
        
#     # Step 1: Extract hashtags
#     hashtags = re.findall(r"#\w+", text)  # Extract hashtags
#     text_without_hashtags = re.sub(r"#\w+", "", text)  # Remove hashtags from text

#     # Step 2: Check if the last sentence ends with an emoji or proper punctuation
#     text_lines = text_without_hashtags.strip().split("\n")  # Split text into lines
#     last_line = text_lines[-1].strip() if text_lines else ""

#     # Regex to check if the last line ends with an emoji
#     emoji_pattern = re.compile(r"[^\w\s,](\s|$)")
#     ends_with_emoji = bool(emoji_pattern.search(last_line))

#     # Add a period if it doesn't end with punctuation or emoji
#     if last_line and not ends_with_emoji and not last_line.endswith(('.', '!', '?')):
#         text_lines[-1] = last_line + "."

#     # Step 3: Reassemble the text without hashtags
#     cleaned_text = "\n".join(text_lines).strip()

#     # Step 4: Append hashtags to the end
#     if hashtags:
#         cleaned_text += " " + " ".join(hashtags)

#     return cleaned_text.strip()


import re

# Function to clean and adjust the string
def Pre_clean_and_complete_string(text):
    """
    Post processing of the reponse layer
    """

    words_with_suffix = [
    "Attention:", "Interest:", "Desire:", "Action:", "Problem:", "Agitation:", 
    "Solution:", "Stop:", "Look:", "Act:", "Purchase:", "Before:", "After:", 
    "Bridge:", "Clear:", "Concise:", "Compelling:", "Credible:", "Features:", 
    "Advantages:", "Benefits:", "Feature:", "Advantage:", "Benefit:"
    ]
    
    # Step 0: Post-Processing of text - Removing words with suffix ":" (case insensitive)
    text = text.replace("|", "")  # Remove "|" from text
    text = text.replace("Relevant Hashtags:", "")  # Remove "Relevant Hashtags:" from text
    text = text.replace("|", "-")

    for word in words_with_suffix:
        input_string = text.replace(word, "")  # Original case
        input_string = text.replace(word.lower(), "")  # Lowercase
        input_string = text.replace(word.upper(), "")  # Uppercase
        input_string = text.replace(word.capitalize(), "")  # Capitalized case
        
    # Step 1: Extract hashtags
    hashtags = re.findall(r"#\w+", text)  # Extract hashtags
    text_without_hashtags = re.sub(r"#\w+", "", text)  # Remove hashtags from text

    # Step 2: Check if the last sentence ends with an emoji or proper punctuation
    text_lines = text_without_hashtags.strip().split("\n")  # Split text into lines
    last_line = text_lines[-1].strip() if text_lines else ""

    # Regex to check if the last line ends with an emoji
    emoji_pattern = re.compile(r"[^\w\s,](\s|$)")
    ends_with_emoji = bool(emoji_pattern.search(last_line))

    # Add a period if it doesn't end with punctuation or emoji
    if last_line and not ends_with_emoji and not last_line.endswith(('.', '!', '?')):
        text_lines[-1] = last_line + "."

    # Step 3: Reassemble the text without hashtags
    cleaned_text = "\n".join(text_lines).strip()

    # Step 4: Append hashtags to the end
    if hashtags:
        cleaned_text += " " + " ".join(hashtags)

    return cleaned_text.strip()



def seprate_text_and_hashtag(text):
    pre_res = Pre_clean_and_complete_string(text)
    # Extract hashtags
    hashtags = re.findall(r"#\w+", pre_res)
    
    # Remove hashtags from text
    text_without_hashtags = re.sub(r"#\w+", "", text).strip()

    return {
        "Hashtag" : hashtags,
        "Text" : text_without_hashtags
    }

def clean_text(text):
    a = seprate_text_and_hashtag(text)
    text_content = a["Text"]
    hashtag_content = a["Hashtag"]

    hashtag_content_unique = list(set(hashtag_content))
    
    
    hashtag_content_string = ""
    for i in hashtag_content_unique:
        
        hashtag_content_string = hashtag_content_string + str(i) + " "
        
        
    
    # Split the text into sentences
    sentences = re.split(r'(?<=\.)\s+', text_content.strip())  # Splits on a period followed by space(s)
    
    # Check the last sentence
    if sentences[-1].endswith("."):
        return {
            "Hashtag" : hashtag_content_string,
            "Text" : text
        }  
    elif '#' in sentences[-1]:
        # Contains hashtag, no change needed
        return {
            "Hashtag" : hashtag_content_string,
            "Text" : text
        }   
    else:
        # Remove the last sentence
        return {
            "Hashtag" : hashtag_content_string,
            "Text" : ' '.join(sentences[:-1])
        }   
        # return ' '.join(sentences[:-1])

# Cleaned text

def clean_and_complete_string(text):
    # Enhanced cleaning for social media content - remove prefacing text and separators

    # Remove common prefacing patterns at the start of text
    prefacing_patterns = [
        r"^Here's a LinkedIn post.*?:\s*",
        r"^Here's a social media post.*?:\s*",
        r"^Here's content.*?:\s*",
        r"^Here's a post.*?:\s*",
        r"^Here's your.*?:\s*",
        r"^Below is.*?:\s*",
        r"^I'll create.*?:\s*",
        r"^I'll write.*?:\s*",
        r"^Here's a natural.*?:\s*",
        r"^Here's a human-first.*?:\s*",
        r"^Here's content written in.*?:\s*"
    ]

    # Remove separator lines
    separator_patterns = [
        r"^---+\s*$",
        r"^\*\*\*+\s*$",
        r"^===+\s*$",
        r"^~~~+\s*$",
        r"^```+\s*$"
    ]

    # Apply prefacing pattern removal
    for pattern in prefacing_patterns:
        text = re.sub(pattern, "", text, flags=re.IGNORECASE | re.MULTILINE)

    # Remove separator lines
    for pattern in separator_patterns:
        text = re.sub(pattern, "", text, flags=re.MULTILINE)

    # Remove empty lines at the beginning
    text = text.lstrip('\n\r ')

    final_cleaned_text = clean_text(text)
    final_text = final_cleaned_text["Text"]
    final_hastag = final_cleaned_text["Hashtag"]

    # Only add hashtags if they exist and aren't empty
    if final_hastag and final_hastag.strip():
        final_clean_res = final_text + "\n\n" + final_hastag
    else:
        final_clean_res = final_text

    final_clean_res = final_clean_res.replace("|", "-")
    final_clean_res = final_clean_res.replace("[", "")
    final_clean_res = final_clean_res.replace("]", "")
    final_clean_res = re.sub(r'#$', '', final_clean_res)

    return final_clean_res.strip()
    
    
# cleaned_text = final_process(text)
# print(cleaned_text)
