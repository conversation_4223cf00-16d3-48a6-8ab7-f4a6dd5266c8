import time
from litellm import completion
import os
import json
import re
from post_processing_layer.post_proc import *
from Model_call.call_model import *
from dotenv import load_dotenv

# Load environment variables from the .env file
load_dotenv()

# Enhanced Tone Intelligence Mapping with Human-Like Language Patterns
TONE_INTELLIGENCE = {
        'Creative': {
            'core_essence': "Inspiring new ideas and thinking outside the box",
            'language_markers': [
                "What if we tried", "I've been experimenting with", "Here's a wild idea",
                "Something clicked when", "This got me thinking", "Breakthrough moment"
            ],
            'emotional_undertone': "Excitement about new possibilities"
        },
        'Curious': {
            'core_essence': "Exploring ideas with wonder and interest",
            'language_markers': [
                "I've been wondering", "Something fascinating happened", "This made me curious",
                "I discovered something", "Here's what I noticed", "It got me thinking"
            ],
            'emotional_undertone': "Intellectual excitement and openness"
        },
        'Empathetic': {
            'core_essence': "Understanding and connecting emotionally with others",
            'language_markers': [
                "I've been there", "We've all felt this", "I hear you",
                "This resonates deeply", "I understand the struggle", "You're not alone"
            ],
            'emotional_undertone': "Compassionate support"
        },
        'Casual': {
            'core_essence': "Friendly, relaxed, and easy-going tone",
            'language_markers': [
                "Just wanted to share", "Quick thought", "This caught my attention",
                "Had to tell you about", "Something cool happened", "Real talk"
            ],
            'emotional_undertone': "Approachable and relatable"
        },
        'Professional': {
            'core_essence': "Authoritative yet approachable business communication",
            'language_markers': [
                "In my experience", "Key insight", "Strategic perspective",
                "Industry observation", "Professional insight", "Business reality"
            ],
            'emotional_undertone': "Confident expertise"
        },
        'Informative': {
            'core_essence': "Educational and knowledge-sharing focused",
            'language_markers': [
                "Here's what I learned", "Important to understand", "Key takeaway",
                "Worth knowing", "Let me explain", "Breaking it down"
            ],
            'emotional_undertone': "Helpful guidance"
        },
        'Engaging': {
            'core_essence': "Captivating and interaction-driving content",
            'language_markers': [
                "What do you think?", "Tell me your experience", "Anyone else notice",
                "Share your thoughts", "Let's discuss", "Your turn"
            ],
            'emotional_undertone': "Interactive enthusiasm"
        },
        'Inspirational': {
            'core_essence': "Motivating and uplifting messaging",
            'language_markers': [
                "You've got this", "Believe in yourself", "Never give up",
                "Dream big", "Make it happen", "Your potential is unlimited"
            ],
            'emotional_undertone': "Motivational energy"
        },
        'Analytical': {
            'core_essence': "Data-driven and logical reasoning",
            'language_markers': [
                "The data shows", "Analysis reveals", "Research indicates",
                "Evidence suggests", "Statistics prove", "Findings demonstrate"
            ],
            'emotional_undertone': "Objective insight"
        },
        'Conversational': {
            'core_essence': "Natural dialogue and storytelling",
            'language_markers': [
                "Let me tell you", "So here's the thing", "You know what",
                "Picture this", "Story time", "Here's what happened"
            ],
            'emotional_undertone': "Friendly storytelling"
        }
    }

def advanced_content_generator(topic, target_audience, tone):
    """
    Sophisticated content generation framework with nuanced emotional intelligence and human-like authenticity.
    Enhanced to produce social media posts that sound genuinely human-written rather than AI-generated.

    :param topic: Core subject of the content
    :param target_audience: Specific professional or demographic group
    :param tone: Emotional and communication style
    """
    # Use the global TONE_INTELLIGENCE mapping

    # Prompt Generation Logic
    def generate_prompt(topic, target_audience, tone):
        tone_data = TONE_INTELLIGENCE.get(tone, TONE_INTELLIGENCE['Informative'])

        prompt = f"""
        You are a skilled social media content creator who writes posts that feel authentically human and naturally engaging. Your goal is to create a LinkedIn post that sounds like it was written by a real person sharing genuine insights, not an AI.

        HUMAN-FIRST WRITING APPROACH:

        AUTHENTICITY MARKERS:
        - Write like you're sharing a personal insight with a colleague over coffee
        - Use conversational language with natural imperfections (contractions, casual transitions)
        - Include subtle personal touches: "I've noticed...", "Here's what struck me...", "Something I learned..."
        - Avoid AI-typical phrases like "In today's fast-paced world" or "Let's dive deep"

        EMOTIONAL INTELLIGENCE:
        - Core Essence: {tone_data['core_essence']}
        - Natural Language Patterns: {', '.join(tone_data['language_markers'][:3])}
        - Emotional Undertone: {tone_data['emotional_undertone']}

        TARGET AUDIENCE: {target_audience}
        - Tailor language complexity and examples to resonate with this specific group
        - Use industry-relevant terminology when appropriate
        - Address their likely pain points, interests, and aspirations

        TOPIC FOCUS: {topic}
        - Find a unique angle or personal perspective on this topic
        - Include specific, concrete examples rather than abstract concepts
        - Connect to current trends or timely relevance when possible

        STRUCTURE FOR MAXIMUM IMPACT:
        - Opening: Personal hook or relatable scenario (1-2 lines)
        - Body: Core insight with supporting details (3-4 short paragraphs)
        - Closing: Thoughtful question or call for shared experiences
        - Length: 100-130 words maximum
        - Emojis: 2-3 relevant ones that enhance, don't distract
        - Hashtags: 2-3 specific, non-generic tags

        HUMAN WRITING SECRETS:
        - Include a small "aha moment" or realization
        - Use specific details instead of broad generalizations
        - Show, don't just tell (use mini-stories or examples)
        - Write with genuine curiosity about the topic: {topic}
        - Let your {tone} personality shine through naturally

        ENGAGEMENT OPTIMIZATION:
        - Start with a scroll-stopping first line
        - Use line breaks for visual appeal and readability
        - End with a question that invites genuine responses
        - Include relatable scenarios your audience faces
        - Balance professional insight with personal authenticity

        CRITICAL OUTPUT REQUIREMENTS:
        🚨 NEVER include any prefacing text like:
        - "Here's a LinkedIn post..."
        - "Here's a social media post..."
        - "Here's content written in..."
        - Any introductory explanations or meta-commentary

        🚨 NEVER include formatting separators like:
        - "---" dividers
        - "***" separators
        - Any formatting instructions or dividers

        🚨 START IMMEDIATELY with the engaging hook/opening line
        - Begin directly with your scroll-stopping first sentence
        - No preamble, no setup, no introduction
        - Jump straight into the content

        🚨 OUTPUT ONLY the raw social media post content that users can immediately copy and paste to their platforms

        Remember: Users must be able to copy your output directly to LinkedIn, Twitter, Facebook without ANY editing required.

        Write a {tone} post about {topic} for {target_audience} that feels genuinely human and drives meaningful engagement.
        """
        return prompt

    return generate_prompt(topic, target_audience, tone)


def create_rewrite_prompt(original_content, target_audience, tone):
    """
    Create a specialized prompt for content refinement and optimization.

    :param original_content: The content to be rewritten and optimized
    :param target_audience: Specific professional or demographic group
    :param tone: Emotional and communication style
    """

    tone_data = TONE_INTELLIGENCE.get(tone, TONE_INTELLIGENCE['Informative'])

    prompt = f"""
    You are an expert content optimizer who specializes in refining and enhancing social media content to maximize engagement and authenticity. Your goal is to transform the provided content into a more compelling, human-like post that resonates deeply with the target audience.

    CONTENT REFINEMENT APPROACH:
    - Enhance clarity and readability while maintaining the core message
    - Optimize for maximum engagement and shareability
    - Ensure the content sounds authentically human-written, not AI-generated
    - Improve emotional connection with the target audience
    - Strengthen the hook and call-to-action elements
    - Maintain the original intent while making it more impactful

    TARGET AUDIENCE: {target_audience}
    TONE: {tone} - {tone_data['core_essence']}

    TONE CHARACTERISTICS TO ENHANCE:
    - Natural Language Patterns: {', '.join(tone_data['language_markers'][:3])}
    - Emotional Undertone: {tone_data['emotional_undertone']}

    OPTIMIZATION REQUIREMENTS:
    - Make the opening hook more scroll-stopping and attention-grabbing
    - Enhance storytelling elements with specific, relatable details
    - Improve the flow and readability of the content
    - Strengthen emotional triggers and psychological engagement
    - Optimize hashtag selection for better discoverability
    - Ensure platform-appropriate length and formatting
    - Add authentic human touches (contractions, natural speech patterns)

    HUMAN-LIKE AUTHENTICITY MARKERS:
    - Use conversational language with natural imperfections
    - Include genuine emotional expressions and reactions
    - Add personal insights or "aha moments"
    - Use specific examples instead of generic statements
    - Show vulnerability or relatability where appropriate
    - Incorporate natural speech patterns and casual contractions

    CRITICAL OUTPUT REQUIREMENTS:
    🚨 NEVER include any prefacing text like:
    - "Here's a refined version..."
    - "Here's the optimized content..."
    - "Here's the rewritten post..."
    - Any introductory explanations or meta-commentary

    🚨 NEVER include formatting separators like:
    - "---" dividers
    - "***" separators
    - Any formatting instructions or dividers

    🚨 START IMMEDIATELY with the enhanced hook/opening line
    - Begin directly with your improved scroll-stopping first sentence
    - No preamble, no setup, no introduction
    - Jump straight into the optimized content

    🚨 OUTPUT ONLY the refined social media post content that users can immediately copy and paste to their platforms

    ORIGINAL CONTENT TO REFINE AND OPTIMIZE:
    {original_content}

    Transform this content into a more engaging, authentic, and impactful social media post that will drive meaningful audience interaction.
    """

    return prompt


def short_content_generation(topic, tone, target_audience, model_name = None, long_content = None, rewrite_content = None):
    """
    Generate a short content using the advanced content generation framework.

    :param topic: Core subject of the content
    :param target_audience: Specific professional or demographic group
    :param tone: Emotional and communication style
    :param model_name: Optional model name for content generation
    :param long_content: Optional parameter for longer content generation with identical functionality to topic
    :param rewrite_content: Optional parameter for content refinement and optimization
    """

    # Default model (Anthropic)
    default_model = os.getenv("default_model")

    # Determine content input based on parameter priority: rewrite_content > long_content > topic
    if rewrite_content is not None:
        content_input = rewrite_content
        # For rewrite content, use a specialized prompt that focuses on refinement
        prompt = create_rewrite_prompt(content_input, target_audience, tone)
    elif long_content is not None:
        content_input = long_content
        prompt = advanced_content_generator(content_input, target_audience, tone)
    else:
        content_input = topic
        prompt = advanced_content_generator(content_input, target_audience, tone)

    response = model_call_for_short_content_feature(prompt, model_name)

    # response = clean_and_complete_string(response)

    response_list = [response]
    # Convert list to JSON format
    json_data = [{"text": text} for text in response_list]

    # Output JSON as a string
    json_output = json.dumps(json_data, ensure_ascii=False, indent=4)

    # Parse the raw JSON string
    parsed_result = json.loads(json_output)

    # Extract and clean the text content
    cleaned_texts = [{"text": item["text"]} for item in parsed_result]

    return cleaned_texts
