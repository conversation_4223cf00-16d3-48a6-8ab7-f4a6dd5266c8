import time
import json
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from litellm import completion
import os
from dotenv import load_dotenv
from Model_call.model_manager import get_model_manager
from post_processing_layer.post_proc import *
import logging

# Load environment variables
load_dotenv()

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize model manager
model_manager = get_model_manager()

def extract_content_from_url(url: str, timeout: int = 30) -> str:
    """
    Extract text content from a given URL.
    
    Args:
        url (str): URL to extract content from
        timeout (int): Request timeout in seconds
        
    Returns:
        str: Extracted text content
        
    Raises:
        Exception: If URL fetching or content extraction fails
    """
    try:
        logger.info(f"Fetching content from URL: {url}")
        
        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError("Invalid URL format")
        
        # Set headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        # Fetch the webpage
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        # Parse HTML content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
            script.decompose()
        
        # Extract text from common content containers
        content_selectors = [
            'article', 'main', '.content', '.post-content', '.entry-content',
            '.article-content', '.post-body', '.story-body', '.content-body'
        ]
        
        extracted_text = ""
        for selector in content_selectors:
            content_element = soup.select_one(selector)
            if content_element:
                extracted_text = content_element.get_text(strip=True, separator=' ')
                break
        
        # Fallback to body if no specific content container found
        if not extracted_text:
            body = soup.find('body')
            if body:
                extracted_text = body.get_text(strip=True, separator=' ')
        
        # Clean up the text
        extracted_text = re.sub(r'\s+', ' ', extracted_text)  # Replace multiple whitespace with single space
        extracted_text = re.sub(r'\n+', '\n', extracted_text)  # Replace multiple newlines with single newline
        
        if len(extracted_text.strip()) < 100:
            raise Exception("Insufficient content extracted from URL")
        
        logger.info(f"Successfully extracted {len(extracted_text)} characters from URL")
        return extracted_text.strip()
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to fetch URL: {str(e)}")
        raise Exception(f"URL fetching failed: {str(e)}")
    except Exception as e:
        logger.error(f"Content extraction failed: {str(e)}")
        raise Exception(f"Content extraction failed: {str(e)}")


def create_carousel_prompt_from_topic(topic: str, number_of_slides: int, tone: str, target_audience: str) -> str:
    """
    Create a prompt for generating carousel content from a topic using one-shot prompting.

    Args:
        topic (str): The topic to create carousel content about
        number_of_slides (int): Number of slides to generate
        tone (str): Tone of the content
        target_audience (str): Target audience for the content

    Returns:
        str: Formatted prompt for the AI model
    """

    prompt = f"""You are a skilled assistant in creating professional content for carousels. Users will provide the number of slides, the topic, the tone, and the target audience. Your task is to craft catchy and informative content based on these inputs. In case users do not provide Mood default it to Informative. And default Target Audience is General unless mentioned by the user.
Follow this structure and provide JSON output in response.

Example Input:
topic = Advancements happening in AI
Tone/Mood = Informative
Target Audience = Tech Enthusiasts
Slides = 5

Example Output:
{{
"slides": [
  {{
    "id": 1,
    "title": "Unleashing the Future",
    "subtitle": "AI Revolution: Transforming Tomorrow's Reality",
    "description": "Discover how AI is reshaping industries with groundbreaking innovations, revolutionizing the future. Unleash the power of artificial intelligence!"
  }},
  {{
    "id": 2,
    "title": "The Rise of AI",
    "description": "Artificial Intelligence, the game-changer, is propelling industries forward. With AI adoption expected to grow by 55% in 2021, the impact is undeniable."
  }},
  {{
    "id": 3,
    "title": "Innovating Intelligence",
    "description": "Integrate AI for advanced analytics, automation, and decision-making processes. The future lies in marrying human potential with AI capabilities."
  }},
  {{
    "id": 4,
    "title": "AI's Transformative Power",
    "description": "AI-driven personalized experiences, enhanced productivity, and predictive insights bring unprecedented value to businesses across all sectors."
  }},
  {{
    "id": 5,
    "title": "Join the AI Revolution!",
    "subtitle": "Embrace Change Now",
    "description": "Stay ahead in the AI realm with our latest insights and updates. Accelerate your knowledge and grow."
  }}
]
}}

Additional Instructions that cannot be violated:
1. The title for the first slide should be a maximum of 3-4 words. The subtitle for the first slide can be 10-12 words.
2. Keep descriptions for each slide concise: Atleast 2-3 sentences and between 35 to 50 words.
3. Avoid promotional CTAs like "Join our community" or "Follow for more" in the last slide.
4. Ensure content is centered around the topic without instructing readers to take actions like "Explore", "Discover", "Unleash", or "Dive into".
5. Conclude with a strong, insightful final slide.

Now generate carousel content for:
topic = {topic}
Tone/Mood = {tone}
Target Audience = {target_audience}
Slides = {number_of_slides}"""

    return prompt


def create_carousel_prompt_from_article(article_content: str, number_of_slides: int, tone: str, target_audience: str) -> str:
    """
    Create a prompt for generating carousel content from article content using one-shot prompting.

    Args:
        article_content (str): The article content to transform
        number_of_slides (int): Number of slides to generate
        tone (str): Tone of the content
        target_audience (str): Target audience for the content

    Returns:
        str: Formatted prompt for the AI model
    """

    # Truncate article content if too long (keep first 3000 characters)
    if len(article_content) > 3000:
        article_content = article_content[:3000] + "..."

    prompt = f"""You are a skilled assistant in creating professional content for carousels. Users will provide the number of slides, the URL of the article, the tone, and the target audience. Your task is to craft catchy and informative content based on these inputs. The post should be from a reader's perspective informing the user's network about the content of the webpage.  In case users do not provide Mood default it to Informative. And default Target Audience is General unless mentioned by the user.
Follow this structure and provide JSON output in response.

Example Input:
Article Link = https://finshots.in/archive/why-is-india-expediting-visa-applications-from-china/
Tone/Mood = Informative
Target Audience = Tech Enthusiasts
Slides = 5

Example Output:
{{
"slides": [
  {{
    "id": 1,
    "title": "Breaking Barriers: India's Visa Revolution",
    "subtitle": "Decoding India's Expedited Chinese Visa Process",
    "description": "Discover why India is fast-tracking visa approvals for selected Chinese individuals. Explore the strategic shift in global business dynamics and India's quest for industrial growth.!"
  }},
  {{
    "id": 2,
    "title": "Strategic Diversification",
    "description": "Learn about the China-plus-one strategy adopted by global economies, including the US and Japan, to de-risk and diversify supply chains. Explore the pivotal role of this strategy in reshaping the global manufacturing landscape.."
  }},
  {{
    "id": 3,
    "title": "The PLI Revolution",
    "description": "Unveil India's cutting-edge Production Linked Incentive scheme aimed at boosting local manufacturing and reducing dependency on Chinese imports. Delve into the incentives, goals, and impact on India's industrial sector."
  }},
  {{
    "id": 4,
    "title": "Streamlining Expertise",
    "description": "Explore how India's streamlined visa process for Chinese technicians under the PLI scheme accelerates industrial growth. Discover the strategic approach to leveraging Chinese expertise while fostering India's manufacturing capabilities.."
  }},
  {{
    "id": 5,
    "title": "Join the Revolution!",
    "subtitle": "Explore Now",
    "description": "Stay updated on insightful financial stories and industry revelations. Follow us for more engaging content and exclusive updates."
  }}
]
}}

Additional Instructions that cannot be violated:
1. The title for the first slide should be a maximum of 3-4 words. The subtitle for the first slide can be 10-12 words.
2. Keep descriptions for each slide concise: 2-3 sentences, minimum 40 words.
3. Avoid promotional CTAs like "Join our community" or "Follow for more" in the last slide.
4. Ensure content is centered around the topic without instructing readers to take actions like "Explore", "Discover", "Unleash", or "Dive into".
5. Conclude with a strong, insightful final slide.
6. Try not to use uncommon acronyms, elaborate  a little on those.

Now generate carousel content from this article:
Article Content: {article_content}
Tone/Mood = {tone}
Target Audience = {target_audience}
Slides = {number_of_slides}"""

    return prompt


def create_carousel_prompt_from_text(text_content: str, number_of_slides: int, tone: str, target_audience: str) -> str:
    """
    Create a prompt for generating carousel content from provided text using one-shot prompting.

    Args:
        text_content (str): The text content to transform
        number_of_slides (int): Number of slides to generate
        tone (str): Tone of the content
        target_audience (str): Target audience for the content

    Returns:
        str: Formatted prompt for the AI model
    """

    # Truncate text content if too long (keep first 2500 characters)
    if len(text_content) > 2500:
        text_content = text_content[:2500] + "..."

    prompt = f"""You are a skilled assistant in creating professional content for carousels. Users will provide the number of slides, the tone of expected response, and the target audience along with paragraph(s) of text. Your task is to craft catchy and coherent content based on these inputs. The post should be from a reader's perspective informing the user's network about the content of the paragraph. Do not add any data from your end and stick only to the information mentioned in the text.  In case users do not provide Mood default it to Informative. And default Target Audience is General unless mentioned by the user.
Follow this structure and provide JSON output in response.

Example Input:
Article Link = \"\"\"
McDonald's is a trademark bully!

And it isn't us saying this. It's what Supermac's, Ireland's largest fast-food chain, told the European Union Intellectual Property Office (EUIPO) or the EU's trademark registration authority, while fighting to cancel the McDonald's Big Mac trademark in the EU.

"How silly of Supermac's!", you might think. Big Mac is such a well-known trademark and everyone associates it with McDonald's. So why would anyone want to fight to cancel it?

Well, if we were to put it in one word, we'd say 'revenge'.

Yup! Close to a decade ago, Supermac's wanted to register its name as a trademark in the EU, so that it could expand outside Ireland.

But McDonald's decided to play spoilsport and threw a spanner in the works by opposing this request. Its argument was the clichéd "Hey, their name is too similar to our Big Mac burgers. So it could confuse our customers."

And this meant that Supermac's partially lost the case. It could keep its restaurant name but couldn't use the Mac label to market the items on its menu.

It was sort of a predictable win too, simply because McDonald's has been successful in winning the most bizarre trademark lawsuits globally in the past. Like its fight against P. C. Mallappa & Co., the Bengaluru based sanitaryware dealer.

In 1994 the American fast-food giant accused it of violating its internationally popular Golden M logo. And despite being in a completely different business field, it had to give up its logo in favour of McDonald's. Courtesy: The Karnataka High Court felt that the similarities between the logos could confuse customers into thinking that P. C. Mallappa is somehow related to McDonald's.

We know how oddly funny that sounds. But you can't really challenge court orders unless you have deep pockets.

Supermac's though, didn't want to be like other small companies. It wanted to stand out from the crowd and stand up for other smaller entrepreneurs like itself. So it took on McDonald's.

But here's the thing. You can't just fight a trademark lawsuit without proving a few basic things.

For instance, your trademark or logo shouldn't be so similar that it confuses customers. Could consumers confuse a Supermac for a grand version of the Big Mac? Well, maybe.

Or you shouldn't be dealing in the same kind of business. In Supermac's case it obviously was, since it was a fast-food restaurant.

Even then, the type of items on your menu matter too. Say if a fast-food restaurant wants to use the word Mac or Mc to brand its Malaysian or Indian cuisine, which is starkly different from American food, it can. That's what the Malaysian Federal Court decided when McDonald's brought a trademark violation case against McCurry, a small Indian curry shop in Malaysia.

And finally, you have to make sure that you've registered your trademark before your opponent.

Now, if you look at all of these minute intricacies, it might seem like Supermac's was obviously set to lose the case. But no. Supermac's had done some great homework. Or rather some basic homework that probably no one else would think of.

It simply went digging to look for how genuinely McDonald's was using the Big Mac trademark because that's also an important factor in most global trademark laws. You can't just register a trademark and go about claiming your exclusive right to use it unless you've used it enough.

Just look at how the Big Mac is marketed in India. The Big Mac essentially has a 100% beef meat patty. That's the kind of image McDonald's has portrayed for the Big Mac world over. But since most Indian regions are sensitive towards beef consumption, McDonald's had to only restrict itself to other meats like chicken or mutton. And selling a non-beef version of it, but still calling it Big Mac wouldn't make sense. That's why McDonald's calls this the Maharaja Mac in India.
\"\"\"
Tone/Mood = Informative
Target Audience = Tech Enthusiasts
Slides = 5

Example Output:
{{
"slides": [
  {{
    "id": 1,
    "title": "Trademark Wars Unveiled!",
    "subtitle": "The Big Mac Battle: Supermac's vs. McDonald's",
    "description": "Delve into the intriguing trademark war between Supermac's and McDonald's. Uncover the secrets behind cancelling the iconic Big Mac trademark in the EU!"
  }},
  {{
    "id": 2,
    "title": "David vs. Goliath: The Legal Duel",
    "description": "Witness the epic showdown as Supermac's challenges McDonald's trademark dominance. Explore the intricacies of trademark laws, business distinctions, and the power of consumer confusion in this clash of the fast-food titans."
  }},
  {{
    "id": 3,
    "title": "Behind the Scenes:The Beef Dilemma",
    "description": "Uncover the beefy controversy surrounding the Big Mac trademark in India. Discover how cultural sensitivities shaped the 'Maharaja Mac' and the strategic moves that redefine the fast-food trademark battleground."
  }},
  {{
    "id": 4,
    "title": "Strategic Trademark Tactics",
    "description": "Learn the trademark playbook Supermac's used in their battle against McDonald's. Explore the significance of trademark usage, branding loopholes, and the strategic moves that redefine trademark warfare in the global arena."
  }},
  {{
    "id": 5,
    "title": "Join the Trademark Insights Journey!",
    "subtitle": "Trademark Triumph!",
    "description": "Unleash exclusive trademark insights and legal battles by following our LinkedIn page. Stay up-to-date with the latest trademark wars and brand clashes in the fast-food industry."
  }}
]
}}

Additional Instructions that cannot be violated:
1. The title for the first slide should be a maximum of 3-4 words. The subtitle for the first slide can be 10-12 words.
2. Keep descriptions for each slide concise: 2-3 sentences, minimum 40 words.
3. Avoid promotional CTAs like "Join our community" or "Follow for more" in the last slide.
4. Ensure content is centered around the topic without instructing readers to take actions like "Explore", "Discover", "Unleash", or "Dive into".
5. Conclude with a strong, insightful final slide.
6. Try not to use uncommon acronyms, elaborate  a little on those.

Now generate carousel content from this text:
Text Content: {text_content}
Tone/Mood = {tone}
Target Audience = {target_audience}
Slides = {number_of_slides}"""

    return prompt


def call_model_for_carousel_generation(prompt: str, model_name: str = None, generation_mode: str = "from_topic") -> str:
    """
    Call the AI model for carousel content generation with mode-specific optimized parameters.

    Args:
        prompt (str): The formatted prompt for carousel generation
        model_name (str, optional): Model name to use for generation
        generation_mode (str): The generation mode to determine parameter optimization

    Returns:
        str: Generated carousel content as JSON string

    Raises:
        Exception: If model call fails
    """
    try:
        logger.info(f"Calling model for carousel generation: {model_name}, mode: {generation_mode}")

        # Use recommended model if none specified (prefer gpt-4o for carousel content)
        if not model_name:
            model_name = "azure/gpt-4o"

        # Mode-specific parameter optimization based on your specifications
        if generation_mode == "from_topic":
            # Topic mode parameters
            temperature = 0.86
            max_tokens = 3600
            top_p = 0.58
            frequency_penalty = 0.68
            presence_penalty = 0.53
        elif generation_mode == "from_article":
            # Article mode parameters
            temperature = 0.86
            max_tokens = 3600
            top_p = 0.52
            frequency_penalty = 0.39
            presence_penalty = 0.33
        elif generation_mode == "from_text":
            # Text mode parameters
            temperature = 0.86
            max_tokens = 3600
            top_p = 0.52
            frequency_penalty = 0.46
            presence_penalty = 0.33
        else:
            # Default parameters
            temperature = 0.86
            max_tokens = 3600
            top_p = 0.55
            frequency_penalty = 0.53
            presence_penalty = 0.43

        # Use ModelManager for enhanced model calling with Helicone integration
        result = model_manager.call_model_with_fallback(
            messages=[{"role": "user", "content": prompt}],
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            frequency_penalty=frequency_penalty,
            presence_penalty=presence_penalty,
            use_case="carousel_content_generation"
        )

        # Extract the content from the response
        if result and "response" in result and "choices" in result["response"]:
            content = result["response"]["choices"][0]["message"]["content"]
            logger.info("Successfully generated carousel content")
            return content
        else:
            raise Exception("Invalid response format from model")

    except Exception as e:
        logger.error(f"Model call failed for carousel generation: {str(e)}")
        raise Exception(f"Carousel generation failed: {str(e)}")


def clean_json_string(json_str: str) -> str:
    """
    Clean common JSON formatting issues in AI model responses.

    Args:
        json_str (str): Raw JSON string from model

    Returns:
        str: Cleaned JSON string
    """
    # Remove all types of markdown code blocks more thoroughly
    json_str = re.sub(r'```json\s*\n?', '', json_str, flags=re.IGNORECASE)
    json_str = re.sub(r'```\s*\n?', '', json_str, flags=re.MULTILINE)
    json_str = re.sub(r'^```.*?\n', '', json_str, flags=re.MULTILINE)
    json_str = re.sub(r'```\s*$', '', json_str, flags=re.MULTILINE)

    # Remove any leading/trailing whitespace
    json_str = json_str.strip()

    # Extract JSON content between first { and last }
    start_idx = json_str.find('{')
    end_idx = json_str.rfind('}')
    if start_idx >= 0 and end_idx > start_idx:
        json_str = json_str[start_idx:end_idx + 1]

    # Fix trailing commas before closing brackets/braces
    json_str = re.sub(r',\s*([}\]])', r'\1', json_str)

    # Fix any remaining markdown artifacts
    json_str = re.sub(r'```.*?```', '', json_str, flags=re.DOTALL)

    return json_str


def aggressive_json_clean(json_str: str) -> str:
    """
    More aggressive JSON cleaning for problematic responses.

    Args:
        json_str (str): JSON string that failed initial parsing

    Returns:
        str: Aggressively cleaned JSON string
    """
    # Remove ALL markdown artifacts - be extremely aggressive
    json_str = re.sub(r'```json\s*\n', '', json_str, flags=re.IGNORECASE)
    json_str = re.sub(r'```json\s*', '', json_str, flags=re.IGNORECASE)
    json_str = re.sub(r'```[a-zA-Z]*\s*\n', '', json_str, flags=re.IGNORECASE)
    json_str = re.sub(r'```[a-zA-Z]*\s*', '', json_str, flags=re.IGNORECASE)
    json_str = re.sub(r'```\s*\n', '', json_str)
    json_str = re.sub(r'```\s*', '', json_str)
    json_str = re.sub(r'```', '', json_str)

    # Remove any backticks
    json_str = json_str.replace('`', '')

    # Extract JSON content between first { and last }
    start_idx = json_str.find('{')
    end_idx = json_str.rfind('}')

    if start_idx >= 0 and end_idx > start_idx:
        json_str = json_str[start_idx:end_idx + 1]

    # Remove trailing commas before closing brackets/braces
    json_str = re.sub(r',\s*([}\]])', r'\1', json_str)

    # Remove any newlines that might be causing issues
    json_str = re.sub(r'\n\s*', ' ', json_str)

    # Clean up extra whitespace
    json_str = re.sub(r'\s+', ' ', json_str)

    return json_str.strip()


def parse_and_validate_carousel_response(response_content: str) -> dict:
    """
    Parse and validate the carousel response from the AI model.

    Args:
        response_content (str): Raw response content from the model

    Returns:
        dict: Parsed and validated carousel data

    Raises:
        Exception: If parsing or validation fails
    """
    try:
        # Log the raw response for debugging
        logger.debug(f"Raw response content: {response_content[:500]}...")

        # More aggressive markdown removal - handle all possible variations
        cleaned_content = response_content.strip()

        # Remove markdown code blocks with all possible patterns
        cleaned_content = re.sub(r'```json\s*\n', '', cleaned_content, flags=re.IGNORECASE)
        cleaned_content = re.sub(r'```json\s*', '', cleaned_content, flags=re.IGNORECASE)
        cleaned_content = re.sub(r'```\s*\n', '', cleaned_content)
        cleaned_content = re.sub(r'```\s*$', '', cleaned_content)
        cleaned_content = re.sub(r'```', '', cleaned_content)

        # Remove any remaining backticks
        cleaned_content = cleaned_content.replace('`', '')

        # Extract JSON content between first { and last }
        start_idx = cleaned_content.find('{')
        end_idx = cleaned_content.rfind('}')

        if start_idx >= 0 and end_idx > start_idx:
            json_str = cleaned_content[start_idx:end_idx + 1]
        else:
            json_str = cleaned_content.strip()

        # Additional cleaning for any remaining issues
        json_str = json_str.strip()

        # Remove any trailing commas before closing brackets
        json_str = re.sub(r',\s*([}\]])', r'\1', json_str)

        # Parse JSON directly without additional cleaning
        carousel_data = json.loads(json_str)

        # Validate structure
        if "slides" not in carousel_data:
            raise ValueError("Response missing 'slides' key")

        if not isinstance(carousel_data["slides"], list):
            raise ValueError("'slides' must be a list")

        if len(carousel_data["slides"]) == 0:
            raise ValueError("No slides found in response")

        # Validate each slide
        for i, slide in enumerate(carousel_data["slides"]):
            if not isinstance(slide, dict):
                raise ValueError(f"Slide {i+1} is not a dictionary")

            required_keys = ["id", "title", "description"]
            for key in required_keys:
                if key not in slide:
                    raise ValueError(f"Slide {i+1} missing required key: {key}")

            # First slide should have subtitle
            if i == 0 and "subtitle" not in slide:
                logger.warning("First slide missing subtitle - this is recommended")

        logger.info(f"Successfully validated carousel with {len(carousel_data['slides'])} slides")
        return carousel_data

    except json.JSONDecodeError as e:
        logger.error(f"JSON parsing failed: {str(e)}")
        logger.error(f"Problematic JSON string: {json_str[:1000]}...")

        # Try one more time with more aggressive cleaning
        try:
            # More aggressive JSON cleaning
            cleaned_json = aggressive_json_clean(response_content)
            logger.debug(f"Aggressively cleaned JSON: {cleaned_json[:500]}...")
            carousel_data = json.loads(cleaned_json)
            logger.info("Successfully parsed JSON after aggressive cleaning")

            # Still need to validate the structure
            if "slides" not in carousel_data:
                raise ValueError("Response missing 'slides' key")
            if not isinstance(carousel_data["slides"], list):
                raise ValueError("'slides' must be a list")
            if len(carousel_data["slides"]) == 0:
                raise ValueError("No slides found in response")

            return carousel_data

        except Exception as aggressive_error:
            logger.error(f"Aggressive cleaning also failed: {str(aggressive_error)}")
            raise Exception(f"Invalid JSON response: {str(e)}. Raw content: {response_content[:500]}...")

    except Exception as e:
        logger.error(f"Carousel validation failed: {str(e)}")
        raise Exception(f"Carousel validation failed: {str(e)}")


def carousel_content_generation(
    generation_mode: str,
    number_of_slides: int,
    tone: str = "Informative",
    target_audience: str = "General",
    model_name: str = None,
    topic: str = None,
    article_url: str = None,
    text_content: str = None
) -> dict:
    """
    Main function for generating carousel content with three different modes.

    Args:
        generation_mode (str): Mode of generation ('from_topic', 'from_article', 'from_text')
        number_of_slides (int): Number of slides to generate
        tone (str): Tone of the content (default: "Informative")
        target_audience (str): Target audience (default: "General")
        model_name (str, optional): Model name for generation
        topic (str, optional): Topic for 'from_topic' mode
        article_url (str, optional): Article URL for 'from_article' mode
        text_content (str, optional): Text content for 'from_text' mode

    Returns:
        dict: Generated carousel content with slides

    Raises:
        Exception: If generation fails or invalid parameters provided
    """
    try:
        logger.info(f"Starting carousel generation - Mode: {generation_mode}, Slides: {number_of_slides}")

        # Validate parameters
        if number_of_slides < 3 or number_of_slides > 10:
            raise ValueError("Number of slides must be between 3 and 10")

        valid_modes = ["from_topic", "from_article", "from_text"]
        if generation_mode not in valid_modes:
            raise ValueError(f"Invalid generation mode. Must be one of: {valid_modes}")

        # Generate prompt based on mode
        if generation_mode == "from_topic":
            if not topic:
                raise ValueError("Topic is required for 'from_topic' mode")
            prompt = create_carousel_prompt_from_topic(topic, number_of_slides, tone, target_audience)

        elif generation_mode == "from_article":
            if not article_url:
                raise ValueError("Article URL is required for 'from_article' mode")
            # Extract content from URL
            article_content = extract_content_from_url(article_url)
            prompt = create_carousel_prompt_from_article(article_content, number_of_slides, tone, target_audience)

        elif generation_mode == "from_text":
            if not text_content:
                raise ValueError("Text content is required for 'from_text' mode")
            prompt = create_carousel_prompt_from_text(text_content, number_of_slides, tone, target_audience)

        # Call model to generate content
        response_content = call_model_for_carousel_generation(prompt, model_name, generation_mode)

        # Parse and validate response
        carousel_data = parse_and_validate_carousel_response(response_content)

        logger.info("Carousel generation completed successfully")
        return carousel_data

    except Exception as e:
        logger.error(f"Carousel generation failed: {str(e)}")
        raise Exception(f"Carousel generation failed: {str(e)}")
