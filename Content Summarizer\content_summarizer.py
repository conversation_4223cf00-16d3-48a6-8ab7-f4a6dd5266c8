import time
import json
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import os
import sys
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Model_call.model_manager import get_model_manager
from post_processing_layer.post_proc import clean_and_complete_string
import logging

# Load environment variables
load_dotenv()

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize model manager
model_manager = get_model_manager()


def extract_content_with_retry(url: str, timeout: int = 30, max_retries: int = 3) -> str:
    """
    Extract text content from a given URL with enhanced robustness and retry logic.
    
    Args:
        url (str): URL to extract content from
        timeout (int): Request timeout in seconds
        max_retries (int): Maximum number of retry attempts
        
    Returns:
        str: Extracted text content
        
    Raises:
        Exception: If URL fetching or content extraction fails after all retries
    """
    last_exception = None
    
    for attempt in range(max_retries):
        try:
            logger.info(f"Attempt {attempt + 1}/{max_retries}: Fetching content from URL: {url}")
            
            # Validate URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError("Invalid URL format")
            
            # Enhanced headers to mimic different browsers
            headers_options = [
                {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                },
                {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-us',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                },
                {
                    'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                }
            ]
            
            headers = headers_options[attempt % len(headers_options)]
            
            # Fetch the webpage with session for better connection handling
            session = requests.Session()
            session.headers.update(headers)
            
            response = session.get(url, timeout=timeout)
            response.raise_for_status()
            
            # Parse HTML content
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove unwanted elements
            for element in soup(["script", "style", "nav", "footer", "header", "aside", "iframe", "noscript", "form", "button"]):
                element.decompose()
            
            # Try multiple extraction strategies
            extracted_text = ""
            
            # Strategy 1: Common article content containers
            content_selectors = [
                'article', 'main', '[role="main"]',
                '.content', '.post-content', '.entry-content', '.article-content',
                '.post-body', '.story-body', '.content-body', '.article-text',
                '.post-text', '.story-content', '.main-content', '.article-body',
                '.entry-body', '.post-entry', '.content-area', '.primary-content'
            ]
            
            for selector in content_selectors:
                content_element = soup.select_one(selector)
                if content_element:
                    extracted_text = content_element.get_text(strip=True, separator=' ')
                    if len(extracted_text.strip()) > 200:  # Ensure substantial content
                        break
            
            # Strategy 2: Look for paragraphs within content areas
            if not extracted_text or len(extracted_text.strip()) < 200:
                content_areas = soup.find_all(['div', 'section'], class_=re.compile(r'content|article|post|story|main', re.I))
                for area in content_areas:
                    paragraphs = area.find_all('p')
                    if len(paragraphs) >= 3:  # At least 3 paragraphs
                        extracted_text = ' '.join([p.get_text(strip=True) for p in paragraphs])
                        if len(extracted_text.strip()) > 200:
                            break
            
            # Strategy 3: Fallback to body with paragraph filtering
            if not extracted_text or len(extracted_text.strip()) < 200:
                body = soup.find('body')
                if body:
                    paragraphs = body.find_all('p')
                    if paragraphs:
                        extracted_text = ' '.join([p.get_text(strip=True) for p in paragraphs if len(p.get_text(strip=True)) > 20])
            
            # Strategy 4: Last resort - full body text
            if not extracted_text or len(extracted_text.strip()) < 100:
                body = soup.find('body')
                if body:
                    extracted_text = body.get_text(strip=True, separator=' ')
            
            # Clean up the text
            extracted_text = re.sub(r'\s+', ' ', extracted_text)  # Replace multiple whitespace with single space
            extracted_text = re.sub(r'\n+', '\n', extracted_text)  # Replace multiple newlines with single newline
            extracted_text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\"\'\n]', '', extracted_text)  # Remove special characters
            
            if len(extracted_text.strip()) < 100:
                raise Exception(f"Insufficient content extracted from URL (only {len(extracted_text)} characters) - article may be behind paywall, require JavaScript, or have minimal content")
            
            logger.info(f"Successfully extracted {len(extracted_text)} characters from URL on attempt {attempt + 1}")
            return extracted_text.strip()
            
        except requests.exceptions.Timeout:
            last_exception = Exception(f"Request timeout after {timeout} seconds on attempt {attempt + 1}")
            logger.warning(f"Timeout on attempt {attempt + 1}, retrying...")
            time.sleep(2 ** attempt)  # Exponential backoff
            
        except requests.exceptions.RequestException as e:
            last_exception = Exception(f"Network error on attempt {attempt + 1}: {str(e)}")
            logger.warning(f"Network error on attempt {attempt + 1}: {str(e)}")
            time.sleep(2 ** attempt)  # Exponential backoff
            
        except Exception as e:
            last_exception = Exception(f"Content extraction error on attempt {attempt + 1}: {str(e)}")
            logger.warning(f"Content extraction error on attempt {attempt + 1}: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(1)
    
    # If all attempts failed, raise the last exception
    logger.error(f"All {max_retries} attempts failed for URL: {url}")
    raise last_exception


def create_summary_prompt(article_content: str, summary_length: str, tone: str, platform: str) -> str:
    """
    Create a specialized prompt for generating content summaries.
    
    Args:
        article_content (str): The extracted article content
        summary_length (str): Length specification ("short", "medium", "long")
        tone (str): Tone for the summary
        platform (str): Target platform optimization
        
    Returns:
        str: Formatted prompt for the AI model
    """
    
    # Length specifications
    length_guidelines = {
        "short": {
            "word_count": "50-100 words",
            "description": "Concise overview capturing only the most essential points",
            "focus": "Core message and key takeaway"
        },
        "medium": {
            "word_count": "100-200 words", 
            "description": "Balanced summary covering main points and supporting details",
            "focus": "Main arguments, key data points, and primary conclusions"
        },
        "long": {
            "word_count": "200-300 words",
            "description": "Comprehensive summary with detailed coverage of all major points",
            "focus": "Complete overview including context, arguments, evidence, and implications"
        }
    }
    
    length_spec = length_guidelines.get(summary_length.lower(), length_guidelines["medium"])
    
    # Platform-specific optimizations
    platform_guidelines = {
        "linkedin": "Professional tone suitable for business networking and thought leadership sharing",
        "twitter": "Engaging and shareable format optimized for social media consumption",
        "facebook": "Accessible and discussion-friendly tone for community engagement",
        "general": "Versatile format suitable for multiple platforms and audiences"
    }
    
    platform_guide = platform_guidelines.get(platform.lower(), platform_guidelines["general"])
    
    prompt = f"""
You are an expert content summarizer who specializes in creating authentic, human-written summaries that capture the essence of articles while maintaining a natural, engaging voice that bypasses AI detection.

**ARTICLE CONTENT TO SUMMARIZE:**
{article_content[:4000]}  # Increased limit for better context

**YOUR TASK:**
Create a {summary_length.upper()} summary of this article with the following specifications:

**LENGTH:** {length_spec['word_count']} - {length_spec['description']}

**FOCUS:** {length_spec['focus']}

**TONE:** {tone}

**PLATFORM OPTIMIZATION:** {platform_guide}

**CONTENT REQUIREMENTS:**
1. **Accuracy:** Capture the main points and key information without distortion
2. **Authenticity:** Write in a genuinely human voice that completely bypasses AI detection
3. **Clarity:** Make complex information accessible and easy to understand
4. **Engagement:** Include compelling elements that draw readers in
5. **Completeness:** Ensure all critical information is covered within the word limit
6. **Objectivity:** Present information fairly without adding personal bias or external opinions

**WRITING STYLE GUIDELINES:**
- Use natural, conversational language with varied sentence structures
- Avoid corporate speak, buzzwords, and AI-typical phrases like "delve into", "leverage", "game-changer"
- Include smooth transitions between ideas
- Use active voice where possible
- Make it feel like a knowledgeable person explaining the article to a friend
- Maintain the original article's factual accuracy

**CRITICAL OUTPUT REQUIREMENTS:**
- Return ONLY the summary content
- NO prefacing text like "Here's a summary" or "This article discusses"
- NO markdown formatting or separators
- Content should be immediately copy-paste ready
- Start directly with the most important information
- End with a strong concluding point

**REMEMBER:** Stay strictly within the bounds of information provided in the article. Your summary should be a faithful representation of the content, not an interpretation or expansion.

Write your {summary_length} summary now:"""

    return prompt


def model_call_for_summary_generation(prompt: str, model_name: str = None) -> str:
    """
    Call the model for summary generation with optimized parameters.

    Args:
        prompt (str): The formatted prompt
        model_name (str): Optional model name

    Returns:
        str: Generated summary content
    """
    try:
        # Use model manager for consistent model calling
        result = model_manager.call_model(
            prompt=prompt,
            model_name=model_name,
            temperature=0.7,   # Balanced temperature for accurate yet natural summaries
            max_tokens=800,    # Sufficient for up to 300-word summaries
            system_prompt="You are an expert content summarizer who creates accurate, engaging summaries that sound authentically human-written."
        )

        return result

    except Exception as e:
        logger.error(f"Model call failed for summary generation: {str(e)}")
        raise Exception(f"Summary generation model call failed: {str(e)}")


def validate_summary_request(data: dict) -> None:
    """
    Validate the summary generation request data.

    Args:
        data (dict): Request data to validate

    Raises:
        ValueError: If required fields are missing or invalid
    """
    required_fields = ["url"]

    for field in required_fields:
        if field not in data:
            raise ValueError(f"Missing required field: '{field}'")

    # Validate URL format
    url = data["url"]
    parsed_url = urlparse(url)
    if not parsed_url.scheme or not parsed_url.netloc:
        raise ValueError(f"Invalid URL format: '{url}'")

    # Validate summary_length if provided
    if "summary_length" in data:
        valid_lengths = ["short", "medium", "long"]
        if data["summary_length"].lower() not in valid_lengths:
            raise ValueError(f"Invalid summary_length: '{data['summary_length']}'. Must be one of: {', '.join(valid_lengths)}")

    # Validate optional fields if provided
    if "tone" in data and not isinstance(data["tone"], str):
        raise ValueError("Tone must be a string")

    if "platform" in data and not isinstance(data["platform"], str):
        raise ValueError("Platform must be a string")


def content_summarizer(
    url: str,
    summary_length: str = "medium",
    tone: str = "neutral",
    platform: str = "general",
    model_name: str = None
) -> list:
    """
    Generate concise, accurate summaries of articles from URLs.

    Args:
        url (str): The post/article URL to summarize
        summary_length (str): Length specification - "short" (50-100), "medium" (100-200), "long" (200-300) words
        tone (str): Tone for the summary (default: "neutral")
        platform (str): Target platform optimization (default: "general")
        model_name (str): Optional model name for content generation

    Returns:
        list: List containing the generated summary in the standard format

    Raises:
        Exception: If URL fetching, content extraction, or generation fails
    """
    try:
        logger.info(f"Starting content summarization for URL: {url} with length: {summary_length}")

        # Extract article content from URL with retry logic
        article_content = extract_content_with_retry(url)

        # Create specialized prompt for summary generation
        prompt = create_summary_prompt(article_content, summary_length, tone, platform)

        # Generate summary content using the model
        response = model_call_for_summary_generation(prompt, model_name)

        # Clean the response using existing post-processing
        cleaned_response = clean_and_complete_string(response)

        # Format response consistently with other content modules
        response_list = [cleaned_response]
        json_data = [{"text": text} for text in response_list]

        logger.info(f"Successfully generated {summary_length} summary with {len(cleaned_response)} characters")
        return json_data

    except Exception as e:
        logger.error(f"Content summarization failed: {str(e)}")
        raise Exception(f"Content summarization failed: {str(e)}")


# Production-ready module - no test functions or hardcoded URLs
# For testing, use the dedicated test scripts or API endpoints
