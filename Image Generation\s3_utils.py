"""
AWS S3 Utilities for Image Generation
====================================

This module provides utilities for uploading generated images to AWS S3 bucket
with proper error handling, logging, and configuration management.

Features:
- Upload images from URLs or base64 data
- Automatic file naming with timestamps
- Public URL generation
- Comprehensive error handling
- Logging and monitoring
"""

import os
import base64
import logging
import requests
from typing import Union, Optional
from datetime import datetime, timezone
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
BASE_DIR = Path(__file__).resolve().parent.parent
load_dotenv(dotenv_path=BASE_DIR / ".env")

# Setup logging
logger = logging.getLogger(__name__)

# S3 Configuration
S3_BUCKET_NAME = "growero-staging"
S3_FOLDER = "ai-generations"

def get_s3_client():
    """
    Initialize and return a configured boto3 S3 client.
    
    Returns:
        boto3.client: Configured S3 client
        
    Raises:
        Exception: If AWS credentials are missing or invalid
    """
    try:
        import boto3
        from botocore.exceptions import ClientError, NoCredentialsError
        
        # Get AWS credentials from environment variables
        aws_access_key = os.getenv("litellm_access_key")
        aws_secret_key = os.getenv("litellm_secret_key")
        aws_region = os.getenv("AWS_REGION_NAME", "ap-south-1")
        
        if not aws_access_key or not aws_secret_key:
            raise Exception("AWS credentials not found. Please check litellm_access_key and litellm_secret_key in .env file")
        
        # Initialize S3 client with explicit credentials
        s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key,
            region_name=aws_region
        )
        
        logger.info(f"S3 client initialized successfully for region: {aws_region}")
        return s3_client
        
    except ImportError:
        raise Exception("boto3 package not installed. Please install with: pip install boto3")
    except NoCredentialsError:
        raise Exception("AWS credentials not found or invalid")
    except Exception as e:
        logger.error(f"Failed to initialize S3 client: {str(e)}")
        raise Exception(f"S3 client initialization failed: {str(e)}")

def download_image_from_url(image_url: str, timeout: int = 30) -> bytes:
    """
    Download image from URL and return as bytes.
    
    Args:
        image_url (str): URL of the image to download
        timeout (int): Request timeout in seconds
        
    Returns:
        bytes: Image data as bytes
        
    Raises:
        Exception: If download fails
    """
    try:
        logger.info(f"Downloading image from URL: {image_url}")
        response = requests.get(image_url, timeout=timeout)
        response.raise_for_status()
        
        logger.info(f"Successfully downloaded image, size: {len(response.content)} bytes")
        return response.content
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to download image from URL: {str(e)}")
        raise Exception(f"Image download failed: {str(e)}")

def decode_base64_image(base64_data: str) -> bytes:
    """
    Decode base64 image data to bytes.
    
    Args:
        base64_data (str): Base64 encoded image data
        
    Returns:
        bytes: Decoded image data
        
    Raises:
        Exception: If decoding fails
    """
    try:
        logger.info("Decoding base64 image data")
        image_bytes = base64.b64decode(base64_data)
        logger.info(f"Successfully decoded base64 image, size: {len(image_bytes)} bytes")
        return image_bytes
        
    except Exception as e:
        logger.error(f"Failed to decode base64 image: {str(e)}")
        raise Exception(f"Base64 decoding failed: {str(e)}")

def generate_s3_key(filename: str, folder: str = S3_FOLDER) -> str:
    """
    Generate S3 key (path) for the image file.
    
    Args:
        filename (str): Name of the file
        folder (str): S3 folder path
        
    Returns:
        str: Complete S3 key
    """
    return f"{folder}/{filename}"

def generate_s3_url(bucket_name: str, s3_key: str) -> str:
    """
    Generate public S3 URL for the uploaded image.
    
    Args:
        bucket_name (str): S3 bucket name
        s3_key (str): S3 key (path) of the file
        
    Returns:
        str: Public S3 URL
    """
    return f"https://{bucket_name}.s3.amazonaws.com/{s3_key}"

def upload_image_to_s3(image_data: Union[str, bytes], 
                      filename: str, 
                      is_base64: bool = False,
                      content_type: str = "image/png") -> str:
    """
    Upload image to AWS S3 bucket and return the public S3 URL.
    
    Args:
        image_data (Union[str, bytes]): Image data (base64 string, URL, or bytes)
        filename (str): Filename for the S3 object
        is_base64 (bool): Whether the image_data is base64 encoded
        content_type (str): MIME type of the image
    
    Returns:
        str: Public S3 URL of the uploaded image
        
    Raises:
        Exception: If upload fails
    """
    try:
        logger.info(f"Starting S3 upload for file: {filename}")
        
        # Get S3 client
        s3_client = get_s3_client()
        
        # Prepare image data for upload
        if is_base64:
            # Decode base64 image
            image_bytes = decode_base64_image(image_data)
        elif isinstance(image_data, str):
            # Download image from URL
            image_bytes = download_image_from_url(image_data)
        else:
            # Already bytes
            image_bytes = image_data
        
        # Generate S3 key
        s3_key = generate_s3_key(filename)
        
        # Upload to S3 (without ACL since bucket doesn't support ACLs)
        logger.info(f"Uploading to S3: bucket={S3_BUCKET_NAME}, key={s3_key}")
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=s3_key,
            Body=image_bytes,
            ContentType=content_type
            # Note: ACL removed as bucket doesn't support ACLs
            # Public access should be configured at bucket level
        )
        
        # Generate public S3 URL
        s3_url = generate_s3_url(S3_BUCKET_NAME, s3_key)
        
        logger.info(f"Successfully uploaded image to S3: {s3_url}")
        return s3_url
        
    except Exception as e:
        logger.error(f"S3 upload failed for file {filename}: {str(e)}")
        raise Exception(f"S3 upload failed: {str(e)}")

def verify_s3_upload(s3_url: str, timeout: int = 10) -> bool:
    """
    Verify that the uploaded image is accessible via the S3 URL.
    
    Args:
        s3_url (str): S3 URL to verify
        timeout (int): Request timeout in seconds
        
    Returns:
        bool: True if image is accessible, False otherwise
    """
    try:
        logger.info(f"Verifying S3 upload: {s3_url}")
        response = requests.head(s3_url, timeout=timeout)
        
        if response.status_code == 200:
            logger.info("S3 upload verification successful")
            return True
        else:
            logger.warning(f"S3 upload verification failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.warning(f"S3 upload verification error: {str(e)}")
        return False

def get_s3_file_info(s3_url: str) -> dict:
    """
    Extract file information from S3 URL.
    
    Args:
        s3_url (str): S3 URL
        
    Returns:
        dict: File information including bucket, key, and filename
    """
    try:
        # Parse S3 URL: https://bucket.s3.amazonaws.com/key
        url_parts = s3_url.replace("https://", "").split("/", 1)
        bucket_part = url_parts[0].replace(".s3.amazonaws.com", "")
        s3_key = url_parts[1] if len(url_parts) > 1 else ""
        filename = s3_key.split("/")[-1] if "/" in s3_key else s3_key
        
        return {
            "bucket": bucket_part,
            "key": s3_key,
            "filename": filename,
            "folder": "/".join(s3_key.split("/")[:-1]) if "/" in s3_key else ""
        }
        
    except Exception as e:
        logger.error(f"Failed to parse S3 URL: {str(e)}")
        return {}

def test_s3_connection() -> bool:
    """
    Test S3 connection and permissions.
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        s3_client = get_s3_client()
        
        # Try to list objects in the bucket (limited to 1 for efficiency)
        response = s3_client.list_objects_v2(
            Bucket=S3_BUCKET_NAME,
            MaxKeys=1
        )
        
        logger.info("S3 connection test successful")
        return True
        
    except Exception as e:
        logger.error(f"S3 connection test failed: {str(e)}")
        return False

# Export main functions
__all__ = [
    'upload_image_to_s3',
    'verify_s3_upload',
    'get_s3_file_info',
    'test_s3_connection',
    'get_s3_client'
]
