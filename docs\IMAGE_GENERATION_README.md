# Enhanced Image Generation API

## Overview

The enhanced image generation endpoint provides robust, multi-model image generation capabilities with automatic social media optimization, S3 storage, and comprehensive monitoring through Helicone.

## 🚀 Features

### Multi-Model Support
- **DALL-E 3**: High-quality image generation via LiteLLM with Azure OpenAI
- **Bria-2-3-Fast-gen2**: Fast generation via direct Azure endpoint integration

### Social Media Optimization
- Automatic prompt enhancement for social media readiness
- Quality modifiers: "high resolution", "HD", "4K quality"
- Professional styling: "social media ready", "engaging visual content"
- Optimized for LinkedIn, Instagram, Facebook, and Twitter

### AWS S3 Integration
- Automatic upload to `growero-staging` bucket
- Images stored in `ai-generations/` folder
- Public URLs for immediate sharing
- Support for both base64 and URL image formats

### Helicone Monitoring
- Comprehensive API call logging
- Usage and cost tracking
- Performance metrics
- Error monitoring and analytics

## 📡 API Endpoint

### POST `/generate_image`

Generate high-quality, social media-ready images with automatic S3 storage.

#### Request Format
```json
{
    "data": {
        "prompt": "A futuristic AI robot helping doctors in a modern hospital setting, digital art style",
        "model_name": "dall-e-3"
    },
    "operation": "generate_image"
}
```

#### Supported Models
- `dall-e-3` or `azure/dall-e-3` (default)
- `bria-2-3-fast-gen2` or `bria`

#### Response Format
```json
{
    "result": {
        "success": true,
        "image_url": "https://growero-staging.s3.amazonaws.com/ai-generations/generated_image_dall_e_3_20241229_143022.png",
        "model_used": "dall-e-3",
        "enhanced_prompt": "A futuristic AI robot helping doctors in a modern hospital setting, digital art style, high resolution, HD, 4K quality, professional quality, professional social media ready, engaging visual content, clean, modern, business-appropriate, natural lighting, well-lit",
        "metadata": {
            "original_prompt": "A futuristic AI robot helping doctors in a modern hospital setting, digital art style",
            "generation_time": "12.34 seconds",
            "timestamp": "2024-12-29T14:30:22.123456+00:00",
            "filename": "generated_image_dall_e_3_20241229_143022.png",
            "generation_method": "litellm"
        }
    },
    "status": "success",
    "message": "Image generation completed successfully"
}
```

## 🛠️ Setup and Configuration

### Environment Variables
Ensure these variables are set in your `.env` file:

```env
# DALL-E 3 Configuration
DALLE_AZURE_ENDPOINT=https://your-endpoint.openai.azure.com/
DALLE_AZURE_API_VERSION=2024-02-01
DALLE_AZURE_OPENAI_API_KEY=your_api_key

# Bria Configuration
BRIA_API_KEY=your_bria_api_key

# AWS S3 Configuration
litellm_access_key=your_aws_access_key
litellm_secret_key=your_aws_secret_key
AWS_REGION_NAME=ap-south-1

# Helicone Monitoring
HELICONE_API_KEY=sk-helicone-your-api-key
```

### Dependencies
```bash
pip install boto3 litellm fastapi python-dotenv requests
```

## 🧪 Testing

### Quick Test
```bash
python run_image_tests.py
```

### Comprehensive Tests
```bash
python test_image_generation.py
```

### S3 Utilities Tests
```bash
python test_s3_utils.py
```

### Postman Collection
Import the `postman` file into Postman for interactive testing.

## 📊 Usage Examples

### cURL Examples

#### DALL-E 3 Generation
```bash
curl -X POST "http://localhost:8000/generate_image" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "prompt": "Professional business meeting in a modern office",
      "model_name": "dall-e-3"
    },
    "operation": "generate_image"
  }'
```

#### Bria Generation
```bash
curl -X POST "http://localhost:8000/generate_image" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "prompt": "Creative workspace with designers collaborating",
      "model_name": "bria-2-3-fast-gen2"
    },
    "operation": "generate_image"
  }'
```

### Python Client Example
```python
import requests

def generate_image(prompt, model="dall-e-3"):
    payload = {
        "data": {
            "prompt": prompt,
            "model_name": model
        },
        "operation": "generate_image"
    }
    
    response = requests.post(
        "http://localhost:8000/generate_image",
        json=payload
    )
    
    if response.status_code == 200:
        result = response.json()
        return result['result']['image_url']
    else:
        raise Exception(f"Generation failed: {response.text}")

# Usage
image_url = generate_image("Sunset over mountains, professional quality")
print(f"Generated image: {image_url}")
```

## 🔧 Architecture

### Components
1. **Image Generation Module** (`Image Generation/image_generation.py`)
   - Multi-model routing
   - Prompt enhancement
   - Integration with S3 utilities

2. **S3 Utilities Module** (`Image Generation/s3_utils.py`)
   - AWS S3 client configuration
   - Image upload handling (base64 and URL)
   - Error handling and verification
   - Public URL generation

3. **Model Manager Integration** (`Model_call/model_manager.py`)
   - DALL-E 3 via LiteLLM
   - Helicone logging
   - Error handling and fallbacks

4. **Main API Endpoint** (`main.py`)
   - Request validation
   - Response formatting
   - MongoDB logging

### Data Flow
1. **Request** → Validate prompt and model
2. **Enhancement** → Optimize prompt for social media
3. **Generation** → Route to appropriate model
4. **Storage** → Upload to S3 bucket
5. **Response** → Return S3 URL and metadata
6. **Logging** → Store in MongoDB and Helicone

## 🚨 Error Handling

### Common Errors
- **400**: Missing prompt or invalid model name
- **500**: API failures, S3 upload issues, or generation errors

### Retry Logic
- Automatic fallbacks for model failures
- S3 upload retry mechanisms
- Comprehensive error logging

## 📈 Monitoring

### Helicone Dashboard
- Real-time API call monitoring
- Cost tracking per model
- Performance metrics
- Error rate analysis

### Logs
- Generation time tracking
- S3 upload success/failure
- Model performance comparison
- User prompt analytics

## 🔒 Security

### API Keys
- Secure environment variable storage
- Separate keys for different services
- No hardcoded credentials

### S3 Access
- Public read access for generated images
- Secure upload with proper ACLs
- Organized folder structure

## 🎯 Best Practices

### Prompt Writing
- Be specific and descriptive
- Include style preferences
- Mention intended use (social media)
- Avoid copyrighted content

### Model Selection
- **DALL-E 3**: High-quality, detailed images
- **Bria**: Faster generation, good for simple images

### Performance
- Cache frequently used prompts
- Monitor generation times
- Use appropriate timeouts
- Handle errors gracefully
