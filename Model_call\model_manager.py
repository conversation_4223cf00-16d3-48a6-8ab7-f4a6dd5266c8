import os
import time
import logging
import litellm
from litellm import completion
from dotenv import load_dotenv
from typing import Optional, Dict, Any, List
import json

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Import monitoring after defining logger to avoid circular imports
try:
    from Model_call.monitoring import monitor_model_call, model_metrics, error_handler, initialize_monitoring
except ImportError:
    # Fallback if monitoring module is not available
    def monitor_model_call(func):
        return func
    model_metrics = None
    error_handler = None
    initialize_monitoring = None

# Helicone integration via LiteLLM callbacks (no separate package needed)
HELICONE_AVAILABLE = True  # LiteLLM has built-in Helicone support
logger.info("Helicone integration available via LiteLLM callbacks")

class LiteLLMConfig:
    """
    Centralized configuration for LiteLLM with multiple Azure OpenAI endpoints.
    Handles model routing, endpoint management, and environment variable setup.
    """

    def __init__(self):
        """Initialize LiteLLM configuration with Azure endpoints and Helicone."""
        self._setup_environment_variables()
        self._setup_helicone_config()
        self.model_mappings = self._create_model_mappings()
        self.endpoint_configs = self._create_endpoint_configs()

    def _setup_environment_variables(self):
        """Setup environment variables for LiteLLM Azure integration."""
        # Primary Azure endpoint (existing)
        os.environ["AZURE_API_KEY"] = os.getenv("AZURE_API_KEY", "")
        os.environ["AZURE_API_BASE"] = os.getenv("AZURE_API_BASE", "")
        os.environ["AZURE_API_VERSION"] = os.getenv("AZURE_API_VERSION", "")

        # Advanced models endpoint (East US 2)
        os.environ["AZURE_API_KEY_EASTUS2"] = os.getenv("AZURE_API_KEY_4_1_O1", "")
        os.environ["AZURE_API_BASE_EASTUS2"] = os.getenv("AZURE_API_BASE_4_1_O1", "")
        os.environ["AZURE_API_VERSION_EASTUS2"] = os.getenv("AZURE_API_VERSION_4_1_O1", "")

        # Standard models endpoint (West Europe)
        os.environ["AZURE_API_KEY_WESTEUROPE"] = os.getenv("AZURE_API_KEY_OTHER", "")
        os.environ["AZURE_API_BASE_WESTEUROPE"] = os.getenv("AZURE_API_BASE_OTHER", "")
        os.environ["AZURE_API_VERSION_WESTEUROPE"] = os.getenv("AZURE_API_VERSION_OTHER", "")

        # AWS credentials for Bedrock
        os.environ["AWS_ACCESS_KEY_ID"] = os.getenv("litellm_access_key", "")
        os.environ["AWS_SECRET_ACCESS_KEY"] = os.getenv("litellm_secret_key", "")
        os.environ["AWS_REGION_NAME"] = os.getenv("AWS_REGION_NAME", "")

    def _setup_helicone_config(self):
        """Setup Helicone configuration for monitoring and observability."""
        # Helicone API configuration
        self.helicone_api_key = os.getenv("HELICONE_API_KEY", "")
        self.helicone_base_url = os.getenv("HELICONE_BASE_URL", "https://oai.helicone.ai")

        # Helicone features configuration
        self.helicone_config = {
            "cache_enabled": os.getenv("HELICONE_CACHE_ENABLED", "true").lower() == "true",
            "retry_enabled": os.getenv("HELICONE_RETRY_ENABLED", "true").lower() == "true",
            "retry_count": int(os.getenv("HELICONE_RETRY_COUNT", "3")),
            "rate_limit_policy": os.getenv("HELICONE_RATE_LIMIT_POLICY", ""),
            "user_id": os.getenv("HELICONE_USER_ID", ""),
            "session_name": os.getenv("HELICONE_SESSION_NAME", "growero-ai-session"),
            "environment": os.getenv("HELICONE_ENVIRONMENT", "production")
        }

        # Setup LiteLLM with Helicone callbacks if API key is available
        if self.helicone_api_key:
            os.environ["HELICONE_API_KEY"] = self.helicone_api_key

            # Configure LiteLLM to use Helicone callbacks (built-in support)
            litellm.success_callback = ["helicone"]
            litellm.failure_callback = ["helicone"]
            logger.info("Helicone monitoring configured successfully with LiteLLM callbacks")
        else:
            logger.warning("HELICONE_API_KEY not found. Helicone monitoring disabled.")
            self.helicone_config = {}

    def _create_model_mappings(self) -> Dict[str, Dict[str, str]]:
        """Create model mappings for LiteLLM with proper Azure endpoint routing."""
        return {
            # Advanced Models (East US 2)
            "azure/gpt-4.1": {
                "model": f"azure/{os.getenv('GPT_4_5_DEPLOYMENT', 'gpt-4.1')}",
                "api_key": os.getenv("AZURE_API_KEY_4_1_O1"),
                "api_base": f"{os.getenv('AZURE_API_BASE_4_1_O1')}/openai/deployments/{os.getenv('GPT_4_5_DEPLOYMENT', 'gpt-4.1')}",
                "api_version": os.getenv("AZURE_API_VERSION_4_1_O1"),
                "endpoint_type": "eastus2"
            },
            # Map gpt-4.5 to gpt-4.1 deployment in backend
            "azure/gpt-4.5": {
                "model": f"azure/{os.getenv('GPT_4_5_DEPLOYMENT', 'gpt-4.1')}",
                "api_key": os.getenv("AZURE_API_KEY_4_1_O1"),
                "api_base": f"{os.getenv('AZURE_API_BASE_4_1_O1')}/openai/deployments/{os.getenv('GPT_4_5_DEPLOYMENT', 'gpt-4.1')}",
                "api_version": os.getenv("AZURE_API_VERSION_4_1_O1"),
                "endpoint_type": "eastus2"
            },
            "azure/o1": {
                "model": f"azure/{os.getenv('O1_DEPLOYMENT', 'o1')}",
                "api_key": os.getenv("AZURE_API_KEY_4_1_O1"),
                "api_base": f"{os.getenv('AZURE_API_BASE_4_1_O1')}/openai/deployments/{os.getenv('O1_DEPLOYMENT', 'o1')}",
                "api_version": os.getenv("AZURE_API_VERSION_4_1_O1"),
                "endpoint_type": "eastus2"
            },

            # Standard Models (West Europe) - ENDPOINT_1
            "azure/gpt-4o": {
                "model": f"azure/{os.getenv('GPT_4O_DEPLOYMENT', 'gpt-4o')}",
                "api_key": os.getenv("AZURE_API_KEY_OTHER"),
                "api_base": f"{os.getenv('AZURE_API_BASE_OTHER')}/openai/deployments/{os.getenv('GPT_4O_DEPLOYMENT', 'gpt-4o')}",
                "api_version": os.getenv("AZURE_API_VERSION_OTHER"),
                "endpoint_type": "westeurope"
            },
            "azure/grok-3": {
                "model": f"azure/{os.getenv('GROK_3_DEPLOYMENT', 'grok-3')}",
                "api_key": os.getenv("AZURE_API_KEY_OTHER"),
                "api_base": f"{os.getenv('AZURE_API_BASE_OTHER')}/openai/deployments/{os.getenv('GROK_3_DEPLOYMENT', 'grok-3')}",
                "api_version": os.getenv("AZURE_API_VERSION_OTHER"),
                "endpoint_type": "westeurope"
            },
            "azure/llama-3.3": {
                "model": f"azure/{os.getenv('LLAMA_3_3_DEPLOYMENT', 'Llama-3.3-70B-Instruc')}",
                "api_key": os.getenv("AZURE_API_KEY_OTHER"),
                "api_base": f"{os.getenv('AZURE_API_BASE_OTHER')}/openai/deployments/{os.getenv('LLAMA_3_3_DEPLOYMENT', 'Llama-3.3-70B-Instruc')}",
                "api_version": os.getenv("AZURE_API_VERSION_OTHER"),
                "endpoint_type": "westeurope"
            },
            "azure/deepseek": {
                "model": f"azure/{os.getenv('DEEPSEEK_DEPLOYMENT', 'DeepSeek-V3-0324')}",
                "api_key": os.getenv("AZURE_API_KEY_OTHER"),
                "api_base": f"{os.getenv('AZURE_API_BASE_OTHER')}/openai/deployments/{os.getenv('DEEPSEEK_DEPLOYMENT', 'DeepSeek-V3-0324')}",
                "api_version": os.getenv("AZURE_API_VERSION_OTHER"),
                "endpoint_type": "westeurope"
            },

            # Legacy Azure models (existing endpoint)
            "azure/gpt-4o-mini": {
                "model": "azure/gpt-4o-mini",
                "api_key": os.getenv("AZURE_API_KEY"),
                "api_base": f"{os.getenv('AZURE_API_BASE')}/openai/deployments/gpt-4o-mini",
                "api_version": os.getenv("AZURE_API_VERSION"),
                "endpoint_type": "legacy"
            },
            "azure/gpt-35-turbo": {
                "model": "azure/gpt-35-turbo",
                "api_key": os.getenv("AZURE_API_KEY"),
                "api_base": f"{os.getenv('AZURE_API_BASE')}/openai/deployments/gpt-35-turbo",
                "api_version": os.getenv("AZURE_API_VERSION"),
                "endpoint_type": "legacy"
            },

            # Image Generation Models
            "azure/dall-e-3": {
                "model": f"azure/{os.getenv('DALLE_DEPLOYMENT', 'dall-e-3')}",
                "api_key": os.getenv("DALLE_AZURE_OPENAI_API_KEY"),
                "api_base": os.getenv('DALLE_AZURE_ENDPOINT'),
                "api_version": os.getenv("DALLE_AZURE_API_VERSION"),
                "endpoint_type": "dalle"
            }
        }

    def _create_endpoint_configs(self) -> Dict[str, Dict[str, Any]]:
        """Create endpoint-specific configurations."""
        return {
            "eastus2": {
                "name": "Advanced Models (East US 2)",
                "base_url": os.getenv("AZURE_API_BASE_4_1_O1"),
                "api_version": os.getenv("AZURE_API_VERSION_4_1_O1"),
                "models": ["azure/gpt-4.1", "azure/gpt-4.5", "azure/o1"],
                "priority": 1,  # Highest priority for advanced models
                "cost_tier": "premium"
            },
            "westeurope": {
                "name": "Standard Models (West Europe) - ENDPOINT_1",
                "base_url": os.getenv("AZURE_API_BASE_OTHER"),
                "api_version": os.getenv("AZURE_API_VERSION_OTHER"),
                "models": ["azure/gpt-4o", "azure/grok-3", "azure/llama-3.3", "azure/deepseek"],
                "priority": 2,
                "cost_tier": "standard"
            },
            "legacy": {
                "name": "Legacy Azure Endpoint",
                "base_url": os.getenv("AZURE_API_BASE"),
                "api_version": os.getenv("AZURE_API_VERSION"),
                "models": ["azure/gpt-4o-mini", "azure/gpt-35-turbo"],
                "priority": 3,
                "cost_tier": "budget"
            },
            "dalle": {
                "name": "DALL-E Image Generation",
                "base_url": os.getenv("DALLE_AZURE_ENDPOINT"),
                "api_version": os.getenv("DALLE_AZURE_API_VERSION"),
                "models": ["azure/dall-e-3"],
                "priority": 1,
                "cost_tier": "image_generation"
            }
        }

class ModelManager:
    """
    A sophisticated model management system for handling multiple AI models
    with different Azure endpoints and configurations.
    """

    def __init__(self):
        """Initialize the ModelManager with LiteLLM configuration."""
        self.config = LiteLLMConfig()
        self.default_model = os.getenv("default_model", "bedrock/anthropic.claude-3-sonnet-20240229-v1:0")
        self.fallback_models = self._setup_fallback_models()

        # Initialize monitoring
        if initialize_monitoring:
            self.health_monitor = initialize_monitoring(self)
        else:
            self.health_monitor = None

    def _setup_fallback_models(self) -> List[str]:
        """Setup fallback model hierarchy for error recovery."""
        return [
            "azure/gpt-4o",
            "azure/gpt-4o-mini",
            "azure/gpt-35-turbo",
            self.default_model
        ]

    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """Get configuration for a specific model."""
        if model_name in self.config.model_mappings:
            return self.config.model_mappings[model_name]
        return None

    def normalize_model_name(self, model_name: Optional[str]) -> str:
        """Normalize and validate model name."""
        if not model_name:
            return self.default_model

        # Handle legacy GPT model naming
        if isinstance(model_name, str) and model_name.lower().startswith("gpt"):
            if not model_name.startswith("azure/"):
                model_name = f"azure/{model_name}"

        # Special mapping: gpt-4.5 -> gpt-4.1 deployment (backend mapping)
        if model_name == "azure/gpt-4.5" or model_name == "gpt-4.5":
            logger.info("Mapping gpt-4.5 to gpt-4.1 deployment in backend")
            model_name = "azure/gpt-4.5"  # Use the mapped version in our config

        # Check if model exists in our mappings
        if model_name in self.config.model_mappings:
            return model_name

        # Return default if model not found
        logger.warning(f"Model {model_name} not found in mappings, using default: {self.default_model}")
        return self.default_model

    def select_optimal_model(self,
                           content_type: str = "general",
                           complexity: str = "medium",
                           cost_preference: str = "balanced") -> str:
        """
        Intelligently select the optimal model based on requirements.

        Args:
            content_type: Type of content (general, creative, technical, etc.)
            complexity: Complexity level (simple, medium, complex)
            cost_preference: Cost preference (budget, balanced, premium)
        """
        if complexity == "complex" or content_type == "technical":
            if cost_preference == "premium":
                return "azure/gpt-4.1"
            else:
                return "azure/gpt-4o"

        elif complexity == "simple" or cost_preference == "budget":
            return "azure/gpt-4o-mini"

        else:  # medium complexity, balanced cost
            return "azure/gpt-4o"

    def get_available_models(self) -> List[str]:
        """Get list of all available models."""
        return list(self.config.model_mappings.keys())

    def get_models_by_endpoint(self, endpoint_type: str) -> List[str]:
        """Get models available on a specific endpoint."""
        return [
            model for model, config in self.config.model_mappings.items()
            if config.get("endpoint_type") == endpoint_type
        ]

    def is_model_available(self, model_name: str) -> bool:
        """Check if a model is available and configured."""
        config = self.get_model_config(model_name)
        if not config:
            return False

        # Check if required configuration is present
        required_fields = ["api_key", "api_base", "api_version"]
        return all(config.get(field) for field in required_fields)

    def get_endpoint_health(self) -> Dict[str, bool]:
        """Check health status of all endpoints."""
        health_status = {}
        for endpoint_type, endpoint_config in self.config.endpoint_configs.items():
            # Simple health check - verify configuration is present
            health_status[endpoint_type] = bool(
                endpoint_config.get("base_url") and
                endpoint_config.get("api_version")
            )
        return health_status

    def call_model_with_fallback(self,
                               messages: List[Dict[str, str]],
                               model_name: Optional[str] = None,
                               temperature: float = 0.5,
                               max_tokens: int = 250,
                               strict_mode: bool = False,
                               **kwargs) -> Dict[str, Any]:
        """
        Call model with automatic fallback on failure and comprehensive monitoring.

        Args:
            messages: List of message dictionaries
            model_name: Preferred model name
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            strict_mode: If True, don't use fallback models - fail if primary model fails
            **kwargs: Additional parameters for completion

        Returns:
            Model response dictionary
        """
        primary_model = self.normalize_model_name(model_name)

        # In strict mode, only try the primary model
        if strict_mode:
            models_to_try = [primary_model]
            logger.info(f"Strict mode enabled - only trying primary model: {primary_model}")
        else:
            models_to_try = [primary_model] + [m for m in self.fallback_models if m != primary_model]

        last_error = None
        attempts = []

        for attempt_num, model in enumerate(models_to_try):
            start_time = time.time()

            try:
                logger.info(f"Attempt {attempt_num + 1}: Calling model {model}")

                # Get model configuration
                model_config = self.get_model_config(model)
                if not model_config:
                    logger.error(f"No configuration found for model: {model}")
                    raise Exception(f"Model configuration not found: {model}")

                logger.info(f"Model config for {model}: endpoint_type={model_config.get('endpoint_type')}, api_base={model_config.get('api_base')}")

                # Prepare completion parameters
                # Filter out internal parameters that shouldn't be passed to the API
                filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ['use_case']}

                completion_params = {
                    "model": model,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    **filtered_kwargs
                }

                # Add Helicone headers and metadata if available
                if self.config.helicone_api_key:
                    helicone_headers = self._get_helicone_headers(model, kwargs.get("use_case", "general"))
                    completion_params["extra_headers"] = helicone_headers

                    # Add metadata for Helicone custom properties (LiteLLM 1.41.23+)
                    helicone_metadata = {}
                    for key, value in helicone_headers.items():
                        if key.startswith("Helicone-Property-"):
                            # Convert header to metadata format
                            metadata_key = key.replace("Helicone-Property-", "Helicone-Property-")
                            helicone_metadata[metadata_key] = value

                    if helicone_metadata:
                        completion_params["metadata"] = helicone_metadata

                    logger.debug(f"Added Helicone headers for {model}: {list(helicone_headers.keys())}")

                # Add Azure-specific parameters if needed
                if model_config and model.startswith("azure/"):
                    azure_params = {
                        "api_key": model_config.get("api_key"),
                        "api_base": model_config.get("api_base"),
                        "api_version": model_config.get("api_version")
                    }
                    completion_params.update(azure_params)
                    logger.info(f"Azure params for {model}: api_base={azure_params['api_base']}, api_version={azure_params['api_version']}")

                # Make the completion call
                response = completion(**completion_params)
                response_time = time.time() - start_time

                # Record successful call
                if model_metrics:
                    model_metrics.record_call(model, True, response_time)

                logger.info(f"Successfully called model: {model} (response time: {response_time:.2f}s)")

                return {
                    "response": response,
                    "model_used": model,
                    "success": True,
                    "response_time": response_time,
                    "attempts": attempts + [{"model": model, "success": True, "response_time": response_time}]
                }

            except Exception as e:
                response_time = time.time() - start_time
                last_error = e
                error_message = str(e)

                # Record failed call
                if model_metrics:
                    model_metrics.record_call(model, False, response_time, error_message)

                # Categorize error and get recovery strategy
                if error_handler:
                    error_category = error_handler.categorize_error(error_message)
                    recovery_strategy = error_handler.get_recovery_strategy(error_category)

                    logger.warning(f"Model {model} failed (category: {error_category}): {error_message}")

                    # Log deployment not found errors more prominently
                    if "DeploymentNotFound" in error_message or "404" in error_message:
                        logger.error(f"🚨 DEPLOYMENT NOT FOUND: {model} deployment does not exist in Azure. Check your deployment name and Azure portal.")

                    # Apply recovery strategy
                    if recovery_strategy.get("delay", 0) > 0 and attempt_num < len(models_to_try) - 1:
                        delay = recovery_strategy["delay"]
                        logger.info(f"Applying recovery delay: {delay}s")
                        time.sleep(delay)
                else:
                    logger.warning(f"Model {model} failed: {error_message}")

                    # Log deployment not found errors more prominently even without error handler
                    if "DeploymentNotFound" in error_message or "404" in error_message:
                        logger.error(f"🚨 DEPLOYMENT NOT FOUND: {model} deployment does not exist in Azure. Check your deployment name and Azure portal.")

                attempts.append({
                    "model": model,
                    "success": False,
                    "error": error_message,
                    "response_time": response_time
                })

                continue

        # If all models failed
        logger.error(f"All models failed. Last error: {str(last_error)}")

        failure_summary = {
            "total_attempts": len(attempts),
            "models_tried": [attempt["model"] for attempt in attempts],
            "last_error": str(last_error),
            "attempts": attempts
        }

        raise Exception(f"All model calls failed. Summary: {json.dumps(failure_summary, indent=2)}")

    def call_model(self,
                  prompt: str,
                  model_name: Optional[str] = None,
                  temperature: float = 0.5,
                  max_tokens: int = 250,
                  system_prompt: Optional[str] = None,
                  strict_mode: bool = False) -> str:
        """
        Simplified model calling interface.

        Args:
            prompt: User prompt
            model_name: Model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens
            system_prompt: Optional system prompt
            strict_mode: If True, don't use fallback models

        Returns:
            Generated text content
        """
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        result = self.call_model_with_fallback(
            messages=messages,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            strict_mode=strict_mode
        )

        return result["response"]["choices"][0]["message"]["content"]

    def call_image_model(self,
                        prompt: str,
                        model_name: str = "azure/dall-e-3",
                        size: str = "1024x1024",
                        quality: str = "hd",
                        n: int = 1) -> Dict[str, Any]:
        """
        Call image generation model with Helicone logging via LiteLLM.

        Args:
            prompt: Image generation prompt
            model_name: Model to use for image generation
            size: Image size
            quality: Image quality
            n: Number of images to generate

        Returns:
            Dict containing image URL and metadata
        """
        try:
            import litellm

            logger.info(f"Generating image with {model_name} via LiteLLM with Helicone logging")

            # Get model configuration
            model_config = self.config.model_mappings.get(model_name)
            if not model_config:
                raise Exception(f"Model {model_name} not found in configuration")

            # Prepare image generation parameters with proper model configuration
            image_params = {
                "model": model_config["model"],
                "prompt": prompt,
                "size": size,
                "quality": quality,
                "n": n,
                "api_key": model_config["api_key"],
                "api_base": model_config["api_base"],
                "api_version": model_config["api_version"]
            }

            # Add Helicone headers if available
            if self.config.helicone_api_key:
                helicone_headers = self._get_helicone_headers(model_name, "image_generation")
                image_params["extra_headers"] = helicone_headers
                logger.info("Helicone logging enabled for image generation")
            else:
                logger.warning("Helicone API key not found, image generation logging disabled")

            # Use LiteLLM for image generation with automatic Helicone integration
            try:
                response = litellm.image_generation(**image_params)

                # Extract image URL from response
                if hasattr(response, 'data') and len(response.data) > 0:
                    image_url = response.data[0].url
                else:
                    raise Exception("No image data received from model")

            except Exception as litellm_error:
                # Handle LiteLLM parsing errors but check if we got the actual data
                error_str = str(litellm_error)
                if "url" in error_str and "https://" in error_str:
                    # Extract URL from error message
                    import re
                    url_match = re.search(r"'url': '(https://[^']+)'", error_str)
                    if url_match:
                        image_url = url_match.group(1)
                        logger.info(f"Extracted image URL from LiteLLM error: {image_url}")
                    else:
                        raise litellm_error
                else:
                    raise litellm_error

            return {
                "image_url": image_url,
                "model_used": model_name,
                "size": size,
                "quality": quality
            }

        except Exception as e:
            logger.error(f"Image generation failed: {str(e)}")
            raise Exception(f"Image generation failed: {str(e)}")

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get detailed information about a model."""
        config = self.get_model_config(model_name)
        if not config:
            return {"error": "Model not found"}

        endpoint_type = config.get("endpoint_type", "unknown")
        endpoint_config = self.config.endpoint_configs.get(endpoint_type, {})

        return {
            "model_name": model_name,
            "endpoint_type": endpoint_type,
            "endpoint_name": endpoint_config.get("name", "Unknown"),
            "cost_tier": endpoint_config.get("cost_tier", "unknown"),
            "priority": endpoint_config.get("priority", 999),
            "available": self.is_model_available(model_name)
        }

    def list_models_by_cost(self) -> Dict[str, List[str]]:
        """List models grouped by cost tier."""
        cost_groups = {"budget": [], "standard": [], "premium": []}

        for model_name in self.get_available_models():
            info = self.get_model_info(model_name)
            cost_tier = info.get("cost_tier", "standard")
            if cost_tier in cost_groups:
                cost_groups[cost_tier].append(model_name)

        return cost_groups

    def get_recommended_model(self, use_case: str) -> str:
        """Get recommended model for specific use cases."""
        recommendations = {
            "content_rewriting": "azure/gpt-4o",
            "creative_writing": "azure/gpt-4.1",
            "technical_analysis": "azure/gpt-4.1",
            "simple_tasks": "azure/gpt-4o-mini",
            "conversation": "azure/gpt-4o",
            "code_generation": "azure/gpt-4.1",
            "summarization": "azure/gpt-4o-mini",
            "translation": "azure/gpt-4o"
        }

        return recommendations.get(use_case, "azure/gpt-4o")

    def get_monitoring_summary(self) -> Dict[str, Any]:
        """Get comprehensive monitoring summary."""
        if not model_metrics:
            return {"error": "Monitoring not available"}

        return {
            "metrics": {
                "total_calls": dict(model_metrics.call_counts),
                "success_rates": {model: model_metrics.get_success_rate(model) for model in model_metrics.call_counts.keys()},
                "average_response_times": {model: model_metrics.get_average_response_time(model) for model in model_metrics.call_counts.keys()},
                "health_scores": {model: model_metrics.get_model_health_score(model) for model in model_metrics.call_counts.keys()}
            },
            "health": self.health_monitor.get_health_summary() if self.health_monitor else {},
            "recent_errors": {model: model_metrics.get_recent_errors(model, 5) for model in model_metrics.recent_errors.keys()},
            "endpoint_configs": {
                endpoint: {
                    "name": config.get("name"),
                    "models": config.get("models"),
                    "cost_tier": config.get("cost_tier"),
                    "priority": config.get("priority")
                }
                for endpoint, config in self.config.endpoint_configs.items()
            }
        }

    def get_model_performance_report(self, model_name: str) -> Dict[str, Any]:
        """Get detailed performance report for a specific model."""
        if not model_metrics:
            return {"error": "Monitoring not available"}

        return {
            "model_name": model_name,
            "total_calls": model_metrics.call_counts.get(model_name, 0),
            "success_rate": model_metrics.get_success_rate(model_name),
            "average_response_time": model_metrics.get_average_response_time(model_name),
            "health_score": model_metrics.get_model_health_score(model_name),
            "recent_errors": model_metrics.get_recent_errors(model_name, 10),
            "model_info": self.get_model_info(model_name)
        }

    async def run_health_checks(self):
        """Run health checks for all endpoints."""
        if self.health_monitor:
            await self.health_monitor.run_health_checks()
        else:
            logger.warning("Health monitor not available")

    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        endpoint_health = self.get_endpoint_health()
        available_models = self.get_available_models()

        healthy_endpoints = sum(1 for h in endpoint_health.values() if h)
        total_endpoints = len(endpoint_health)

        if model_metrics:
            total_calls = sum(model_metrics.call_counts.values())
            total_successes = sum(model_metrics.success_counts.values())
            overall_success_rate = (total_successes / total_calls) if total_calls > 0 else 0
        else:
            total_calls = 0
            overall_success_rate = 0

        return {
            "status": "healthy" if healthy_endpoints == total_endpoints else "degraded",
            "endpoints": {
                "total": total_endpoints,
                "healthy": healthy_endpoints,
                "health_percentage": (healthy_endpoints / total_endpoints * 100) if total_endpoints > 0 else 0
            },
            "models": {
                "total_available": len(available_models),
                "by_endpoint": {
                    endpoint: len(self.get_models_by_endpoint(endpoint))
                    for endpoint in self.config.endpoint_configs.keys()
                }
            },
            "performance": {
                "total_calls": total_calls,
                "overall_success_rate": overall_success_rate
            },
            "timestamp": time.time()
        }

    def _get_helicone_headers(self, model_name: str, use_case: str = "general") -> Dict[str, str]:
        """Generate Helicone headers for request tracking and monitoring."""
        headers = {}

        if self.config.helicone_api_key:
            headers["Helicone-Auth"] = f"Bearer {self.config.helicone_api_key}"

            # Add Azure-specific headers for proper routing
            model_config = self.get_model_config(model_name)
            if model_config and model_name.startswith("azure/"):
                # Extract base URL without deployment path for Helicone-OpenAI-Api-Base
                api_base = model_config.get("api_base", "")
                if "/openai/deployments/" in api_base:
                    base_url = api_base.split("/openai/deployments/")[0] + "/"
                    headers["Helicone-OpenAI-Api-Base"] = base_url

                # Add model override to ensure consistent model naming
                deployment_name = model_config.get("model", "").replace("azure/", "")
                if deployment_name:
                    headers["Helicone-Model-Override"] = deployment_name

            # Add optional headers from configuration
            if self.config.helicone_config.get("cache_enabled"):
                headers["Helicone-Cache-Enabled"] = "true"

            if self.config.helicone_config.get("retry_enabled"):
                headers["Helicone-Retry-Enabled"] = "true"
                headers["Helicone-Retry-Count"] = str(self.config.helicone_config.get("retry_count", 3))

            # Add session and user information
            if self.config.helicone_config.get("user_id"):
                headers["Helicone-User-Id"] = self.config.helicone_config["user_id"]

            if self.config.helicone_config.get("session_name"):
                headers["Helicone-Session-Name"] = self.config.helicone_config["session_name"]

            # Add custom properties for better tracking
            headers["Helicone-Property-Environment"] = self.config.helicone_config.get("environment", "production")
            headers["Helicone-Property-Model-Type"] = "azure-openai"
            headers["Helicone-Property-Use-Case"] = use_case
            headers["Helicone-Property-Model-Name"] = model_name

            # Add endpoint information
            if model_config:
                headers["Helicone-Property-Endpoint"] = model_config.get("endpoint_type", "unknown")
                headers["Helicone-Property-Cost-Tier"] = self._get_model_cost_tier(model_name)

        return headers

    def _get_model_cost_tier(self, model_name: str) -> str:
        """Get cost tier for a model."""
        if model_name in ["azure/gpt-4.1", "azure/o1"]:
            return "premium"
        elif model_name in ["azure/gpt-35-turbo", "azure/gpt-4o-mini"]:
            return "budget"
        else:
            return "standard"

    def get_helicone_dashboard_config(self) -> Dict[str, Any]:
        """Get Helicone dashboard configuration for monitoring setup."""
        if not HELICONE_AVAILABLE:
            return {"error": "Helicone not available"}

        return {
            "api_key_configured": bool(self.config.helicone_api_key),
            "helicone_available": HELICONE_AVAILABLE,
            "features": {
                "caching": self.config.helicone_config.get("cache_enabled", False),
                "retries": self.config.helicone_config.get("retry_enabled", False),
                "rate_limiting": bool(self.config.helicone_config.get("rate_limit_policy"))
            },
            "models_tracked": list(self.config.model_mappings.keys()),
            "endpoints_tracked": list(self.config.endpoint_configs.keys()),
            "custom_properties": [
                "Environment", "Model-Type", "Use-Case", "Model-Name",
                "Endpoint", "Cost-Tier"
            ],
            "recommended_dashboard_filters": {
                "by_model": "Helicone-Property-Model-Name",
                "by_use_case": "Helicone-Property-Use-Case",
                "by_cost_tier": "Helicone-Property-Cost-Tier",
                "by_endpoint": "Helicone-Property-Endpoint"
            }
        }


# Global model manager instance
model_manager = ModelManager()


def get_model_manager() -> ModelManager:
    """Get the global model manager instance."""
    return model_manager