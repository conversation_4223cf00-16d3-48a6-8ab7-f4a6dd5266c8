from fastapi import FastAP<PERSON>, Request, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import os
from typing import Optional, Dict, Any
import logging
from dotenv import load_dotenv
import sys
import os

# Add the parent directory to the path to import from Model_call
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Model_call.model_manager import get_model_manager
from Model_call.call_model import model_call_for_short_content_feature
from Short_Content.short_content_generation import advanced_content_generator

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger("app")
error_logger = logging.getLogger("error")

# Get model manager instance
model_manager = get_model_manager()

app = FastAPI(
    title="Content Rewrite API",
    description="Enhanced content rewriting API with multi-model Azure OpenAI support",
    version="2.0.0"
)

# Legacy request models removed - functionality consolidated under /process endpoint

# Legacy helper functions removed - no longer needed after endpoint consolidation


# Legacy /rewrite-ai-post endpoint removed - functionality moved to /process endpoint with operation: "repurpose_content"

# Legacy /rewrite-ai-post-v2 endpoint removed - functionality moved to /process endpoint with operation: "repurpose_content"


# Legacy /rewrite-ai-post-advanced endpoint removed - functionality moved to /process endpoint with operation: "repurpose_content"


# Legacy helper functions removed - functionality consolidated under /process endpoint


# Utility endpoints

@app.get("/models/available")
async def get_available_models():
    """Get list of all available models."""
    try:
        models = model_manager.get_available_models()
        models_info = {}

        for model in models:
            models_info[model] = model_manager.get_model_info(model)

        return {
            "available_models": models,
            "models_info": models_info,
            "total_count": len(models)
        }
    except Exception as e:
        logger.error(f"Error getting available models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/models/recommended/{use_case}")
async def get_recommended_model(use_case: str):
    """Get recommended model for a specific use case."""
    try:
        recommended_model = model_manager.get_recommended_model(use_case)
        model_info = model_manager.get_model_info(recommended_model)

        return {
            "use_case": use_case,
            "recommended_model": recommended_model,
            "model_info": model_info
        }
    except Exception as e:
        logger.error(f"Error getting recommended model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/models/by-cost")
async def get_models_by_cost():
    """Get models grouped by cost tier."""
    try:
        cost_groups = model_manager.list_models_by_cost()
        return {
            "models_by_cost": cost_groups,
            "cost_tiers": list(cost_groups.keys())
        }
    except Exception as e:
        logger.error(f"Error getting models by cost: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health/endpoints")
async def check_endpoint_health():
    """Check health status of all Azure endpoints."""
    try:
        health_status = model_manager.get_endpoint_health()
        return {
            "endpoint_health": health_status,
            "overall_status": "healthy" if all(health_status.values()) else "degraded"
        }
    except Exception as e:
        logger.error(f"Error checking endpoint health: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """General health check endpoint."""
    try:
        endpoint_health = model_manager.get_endpoint_health()
        available_models = len(model_manager.get_available_models())

        return {
            "status": "healthy",
            "service": "Content Rewrite API",
            "version": "2.0.0",
            "endpoints_healthy": all(endpoint_health.values()),
            "available_models": available_models,
            "features": [
                "Multi-model support",
                "Azure OpenAI integration",
                "Intelligent model routing",
                "Fallback mechanisms",
                "Cost optimization"
            ]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def content_rewrite_generation(topic, tone, target_audience, model_name=None, long_content=None, rewrite_content=None, original_content=None):
    """
    Unified content rewrite function that integrates with the new parameter system.

    :param topic: Core subject of the content
    :param tone: Emotional and communication style
    :param target_audience: Specific professional or demographic group
    :param model_name: Optional model name for content generation
    :param long_content: Optional parameter for longer content generation
    :param rewrite_content: Optional parameter for content refinement and optimization
    :param original_content: Optional original content to be rewritten (legacy parameter)
    """

    # Determine content input based on parameter priority: rewrite_content > original_content > long_content > topic
    if rewrite_content is not None:
        content_input = rewrite_content
        operation_type = "rewrite_optimization"
    elif original_content is not None:
        content_input = original_content
        operation_type = "content_rewrite"
    elif long_content is not None:
        content_input = long_content
        operation_type = "long_content_rewrite"
    else:
        content_input = topic
        operation_type = "topic_rewrite"

    # Create specialized prompt based on operation type
    if operation_type == "rewrite_optimization":
        prompt = create_rewrite_optimization_prompt(content_input, target_audience, tone)
    elif operation_type in ["content_rewrite", "long_content_rewrite"]:
        prompt = create_content_rewrite_prompt(content_input, target_audience, tone, topic)
    else:
        # Generate new content based on topic
        prompt = advanced_content_generator(content_input, target_audience, tone)

    # Use the model call function for consistency
    response = model_call_for_short_content_feature(prompt, model_name)

    # Format response consistently with other modules
    response_list = [response]
    json_data = [{"text": text} for text in response_list]

    return json_data


def create_rewrite_optimization_prompt(original_content, target_audience, tone):
    """
    Create a specialized prompt for content optimization and refinement.
    """

    prompt = f"""
    You are an expert content optimizer who specializes in refining and enhancing social media content to maximize engagement and authenticity. Your goal is to transform the provided content into a more compelling, human-like post that resonates deeply with the target audience.

    CONTENT OPTIMIZATION APPROACH:
    - Enhance clarity and readability while maintaining the core message
    - Optimize for maximum engagement and shareability
    - Ensure the content sounds authentically human-written, not AI-generated
    - Improve emotional connection with the target audience
    - Strengthen the hook and call-to-action elements
    - Maintain the original intent while making it more impactful

    TARGET AUDIENCE: {target_audience}
    TONE: {tone}

    OPTIMIZATION REQUIREMENTS:
    - Make the opening hook more scroll-stopping and attention-grabbing
    - Enhance storytelling elements with specific, relatable details
    - Improve the flow and readability of the content
    - Strengthen emotional triggers and psychological engagement
    - Optimize hashtag selection for better discoverability
    - Ensure platform-appropriate length and formatting
    - Add authentic human touches (contractions, natural speech patterns)

    HUMAN-LIKE AUTHENTICITY MARKERS:
    - Use conversational language with natural imperfections
    - Include genuine emotional expressions and reactions
    - Add personal insights or "aha moments"
    - Use specific examples instead of generic statements
    - Show vulnerability or relatability where appropriate
    - Incorporate natural speech patterns and casual contractions

    CRITICAL OUTPUT REQUIREMENTS:
    🚨 NEVER include any prefacing text like:
    - "Here's a refined version..."
    - "Here's the optimized content..."
    - "Here's the rewritten post..."
    - Any introductory explanations or meta-commentary

    🚨 NEVER include formatting separators like:
    - "---" dividers
    - "***" separators
    - Any formatting instructions or dividers

    🚨 START IMMEDIATELY with the enhanced hook/opening line
    - Begin directly with your improved scroll-stopping first sentence
    - No preamble, no setup, no introduction
    - Jump straight into the optimized content

    🚨 OUTPUT ONLY the refined social media post content that users can immediately copy and paste to their platforms

    ORIGINAL CONTENT TO OPTIMIZE:
    {original_content}

    Transform this content into a more engaging, authentic, and impactful social media post that will drive meaningful audience interaction.
    """

    return prompt


def create_content_rewrite_prompt(original_content, target_audience, tone, topic):
    """
    Create a prompt for rewriting existing content with improvements.
    """

    prompt = f"""
    You are an expert content rewriter who specializes in transforming existing content into more engaging, authentic, and platform-optimized social media posts. Your goal is to rewrite the provided content while maintaining its core message but dramatically improving its impact and engagement potential.

    REWRITING APPROACH:
    - Preserve the essential message and key points
    - Transform the structure for better social media performance
    - Enhance readability and visual appeal
    - Add authentic human voice and personality
    - Optimize for platform-specific engagement
    - Strengthen emotional connection with the audience

    TARGET AUDIENCE: {target_audience}
    TONE: {tone}
    TOPIC CONTEXT: {topic}

    REWRITING REQUIREMENTS:
    - Create a compelling opening hook that stops scrolling
    - Break up content into digestible, scannable sections
    - Add relevant emojis and visual elements
    - Include strategic hashtags for discoverability
    - End with an engaging question or call-to-action
    - Ensure content feels authentically human-written

    ENGAGEMENT OPTIMIZATION:
    - Use storytelling techniques and specific examples
    - Include relatable scenarios and personal touches
    - Add conversational elements and natural language
    - Create multiple engagement opportunities throughout
    - Balance professional insight with approachable tone

    CRITICAL OUTPUT REQUIREMENTS:
    🚨 NEVER include any prefacing text like:
    - "Here's a rewritten version..."
    - "Here's the improved content..."
    - "Here's your rewritten post..."
    - Any introductory explanations or meta-commentary

    🚨 NEVER include formatting separators like:
    - "---" dividers
    - "***" separators
    - Any formatting instructions or dividers

    🚨 START IMMEDIATELY with the rewritten content
    - Begin directly with your improved opening hook
    - No preamble, no setup, no introduction
    - Jump straight into the rewritten content

    🚨 OUTPUT ONLY the rewritten social media post content that users can immediately copy and paste to their platforms

    ORIGINAL CONTENT TO REWRITE:
    {original_content}

    Rewrite this content to be more engaging, authentic, and optimized for social media success while maintaining the core message and value.
    """

    return prompt


def content_repurpose_generation(content_input, tone, target_audience, model_name=None):
    """
    Repurpose existing content to be authentically human-written while maintaining similar length.
    Follows the established content generation module patterns for consistency.

    :param content_input: The original content to be repurposed
    :param tone: Emotional and communication style
    :param target_audience: Specific professional or demographic group
    :param model_name: Optional model name for content generation
    """

    try:
        logger.info(f"Repurposing content for audience: {target_audience} with tone: {tone}")

        # Create specialized repurpose prompt
        prompt = create_repurpose_prompt(content_input, target_audience, tone)

        # Use the model call function for consistency with other modules
        response = model_call_for_short_content_feature(prompt, model_name)

        # Format response consistently with other modules
        response_list = [response]
        json_data = [{"text": text} for text in response_list]

        logger.info("Content repurposing completed successfully")
        return json_data

    except Exception as e:
        logger.error(f"Error in content repurposing: {str(e)}")
        raise Exception(f"Content repurposing failed: {str(e)}")


def dedicated_content_rewrite_generation(content_input, tone="professional", target_audience="general", model_name=None):
    """
    Dedicated content rewrite function that uses GPT-4o as default and follows established patterns.
    This function is specifically designed for the rewrite_content operation.

    :param content_input: The original content to be rewritten
    :param tone: Emotional and communication style (default: "professional")
    :param target_audience: Specific professional or demographic group (default: "general")
    :param model_name: Optional model name for content generation (default: GPT-4o via content_rewriting use case)
    """

    try:
        logger.info(f"Starting dedicated content rewrite for audience: {target_audience} with tone: {tone}")

        # Use GPT-4o as default by leveraging the model manager's content_rewriting recommendation
        if not model_name:
            model_name = model_manager.get_recommended_model("content_rewriting")  # Returns azure/gpt-4o
            logger.info(f"Using default model for content rewriting: {model_name}")

        # Create specialized rewrite prompt optimized for authentically human-written content
        prompt = create_dedicated_rewrite_prompt(content_input, target_audience, tone)

        # Use the model call function for consistency with other modules
        response = model_call_for_short_content_feature(prompt, model_name)

        # Format response consistently with other modules
        response_list = [response]
        json_data = [{"text": text} for text in response_list]

        logger.info("Dedicated content rewrite completed successfully")
        return json_data

    except Exception as e:
        logger.error(f"Error in dedicated content rewrite: {str(e)}")
        raise Exception(f"Dedicated content rewrite failed: {str(e)}")


def create_repurpose_prompt(original_content, target_audience, tone):
    """
    Create a specialized prompt for content repurposing that maintains similar length
    while making content authentically human-written and bypassing AI detection.
    """

    prompt = f"""
    You are an expert content repurposer who specializes in transforming existing content into authentically human-written material that completely bypasses AI detection systems. Your goal is to rewrite the provided content while maintaining its core message, similar length, and key information, but making it sound like it was written by a real person sharing genuine insights.

    REPURPOSING APPROACH:
    - Maintain the same approximate length as the original content
    - Preserve all key information and main points
    - Transform the writing style to be authentically human
    - Eliminate any AI-generated patterns or corporate speak
    - Add natural human touches and conversational elements
    - Keep the core message and intent intact

    TARGET AUDIENCE: {target_audience}
    TONE: {tone}

    HUMAN AUTHENTICITY REQUIREMENTS:
    - Use natural, conversational language with imperfections
    - Include genuine emotional expressions and personal touches
    - Add specific, relatable examples instead of generic statements
    - Use contractions and casual speech patterns naturally
    - Show authentic personality and voice
    - Incorporate natural flow and rhythm in sentences
    - Avoid overly polished or corporate language

    ANTI-AI DETECTION STRATEGIES:
    - Vary sentence structure and length naturally
    - Use authentic human speech patterns and idioms
    - Include natural hesitations or qualifiers ("I think", "maybe", "probably")
    - Add personal perspective markers ("In my experience", "I've found")
    - Use specific details rather than broad generalizations
    - Include natural transitions and connectors
    - Avoid repetitive patterns or formulaic structures

    LENGTH MAINTENANCE:
    - Keep the repurposed content approximately the same length as the original
    - Don't significantly expand or compress the content
    - Maintain the same level of detail and information density
    - Preserve the original structure and flow where appropriate

    CRITICAL OUTPUT REQUIREMENTS:
    🚨 NEVER include any prefacing text like:
    - "Here's the repurposed content..."
    - "Here's a human-written version..."
    - "Here's the rewritten content..."
    - Any introductory explanations or meta-commentary

    🚨 NEVER include formatting separators like:
    - "---" dividers
    - "***" separators
    - Any formatting instructions or dividers

    🚨 START IMMEDIATELY with the repurposed content
    - Begin directly with the transformed content
    - No preamble, no setup, no introduction
    - Jump straight into the repurposed material

    🚨 OUTPUT ONLY the repurposed content that users can immediately use
    - Content should be ready for immediate posting/use
    - No additional explanations or instructions
    - Clean, direct output only

    ORIGINAL CONTENT TO REPURPOSE:
    {original_content}

    Transform this content to be authentically human-written while maintaining its core message, similar length, and key information. Make it sound like a real person wrote it naturally, not an AI system.
    """

    return prompt


def create_dedicated_rewrite_prompt(original_content, target_audience, tone):
    """
    Create a specialized prompt for dedicated content rewriting that generates authentically
    human-written content optimized for bypassing AI detection and maintaining similar length.
    """

    prompt = f"""
    You are an expert content rewriter who specializes in transforming existing content into authentically human-written material that completely bypasses AI detection systems. Your goal is to rewrite the provided content while maintaining its core message, similar length, and key information, but making it sound like it was written by a real person with genuine insights and natural expression.

    REWRITING APPROACH:
    - Maintain the same approximate length as the original content
    - Preserve all key information, main points, and essential details
    - Transform the writing style to be authentically human and conversational
    - Eliminate any AI-generated patterns, corporate speak, or formulaic language
    - Add natural human touches, personality, and genuine voice
    - Keep the core message, intent, and value proposition intact

    TARGET AUDIENCE: {target_audience}
    TONE: {tone}

    HUMAN AUTHENTICITY REQUIREMENTS:
    - Use natural, conversational language with authentic imperfections
    - Include genuine emotional expressions and personal perspective markers
    - Add specific, relatable examples instead of generic or abstract statements
    - Use contractions and casual speech patterns naturally ("I've", "don't", "can't")
    - Show authentic personality and individual voice characteristics
    - Incorporate natural flow, rhythm, and varied sentence structures
    - Avoid overly polished, corporate, or AI-generated language patterns

    ANTI-AI DETECTION STRATEGIES:
    - Vary sentence structure and length in natural, human-like patterns
    - Use authentic human speech patterns, idioms, and colloquialisms
    - Include natural hesitations, qualifiers, and human uncertainty markers ("I think", "maybe", "probably", "in my experience")
    - Add personal perspective and experience markers ("I've found", "From what I've seen", "In my opinion")
    - Use specific, concrete details rather than broad generalizations
    - Include natural transitions, connectors, and conversational bridges
    - Avoid repetitive patterns, formulaic structures, or AI-typical phrasing
    - Add subtle human inconsistencies and natural language variations

    LENGTH AND STRUCTURE MAINTENANCE:
    - Keep the rewritten content approximately the same length as the original
    - Don't significantly expand or compress the content unnecessarily
    - Maintain the same level of detail and information density
    - Preserve the original structure and logical flow where appropriate
    - Ensure all key points and important information are retained

    ENGAGEMENT AND AUTHENTICITY:
    - Make the content more engaging and relatable to the target audience
    - Add storytelling elements and human connection points where appropriate
    - Include authentic emotional resonance and genuine insights
    - Use language that feels natural and unforced
    - Create content that sounds like a real person sharing their thoughts

    CRITICAL OUTPUT REQUIREMENTS:
    🚨 NEVER include any prefacing text like:
    - "Here's the rewritten content..."
    - "Here's a human-written version..."
    - "Here's the improved version..."
    - Any introductory explanations or meta-commentary

    🚨 NEVER include formatting separators like:
    - "---" dividers
    - "***" separators
    - Any formatting instructions or dividers

    🚨 START IMMEDIATELY with the rewritten content
    - Begin directly with the transformed content
    - No preamble, no setup, no introduction
    - Jump straight into the rewritten material

    🚨 OUTPUT ONLY the rewritten content that users can immediately use
    - Content should be ready for immediate posting/publishing
    - No additional explanations, instructions, or meta-text
    - Clean, direct output that maintains professional quality

    ORIGINAL CONTENT TO REWRITE:
    {original_content}

    Transform this content to be authentically human-written while maintaining its core message, similar length, and all key information. Make it sound like a real person wrote it naturally, with genuine personality and voice, completely avoiding any AI-generated patterns or detection markers.
    """

    return prompt


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)