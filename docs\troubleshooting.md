# Troubleshooting Guide

Comprehensive troubleshooting guide for the Enhanced FastAPI application with Azure OpenAI integration and Helicone monitoring.

## 🚨 Quick Diagnostics

### Health Check Commands
```bash
# Basic API health
curl -X GET "http://localhost:8000/health"

# Detailed endpoint health
curl -X GET "http://localhost:8000/health/endpoints"

# Configuration validation
curl -X GET "http://localhost:8000/config/validate"

# Monitoring summary
curl -X GET "http://localhost:8000/monitoring/summary"
```

### Environment Validation
```bash
# Check required environment variables
echo "Azure East US 2 Key: ${AZURE_API_KEY_4_1_O1:0:10}..."
echo "Azure West Europe Key: ${AZURE_API_KEY_OTHER:0:10}..."
echo "Helicone Key: ${HELICONE_API_KEY:0:10}..."

# Test model configuration
curl -X POST "http://localhost:8000/config/test-model" \
  -H "Content-Type: application/json" \
  -d '{"model": "azure/gpt-4o", "test_prompt": "Hello"}'
```

## 🔧 Common Issues and Solutions

### 1. Connection and Startup Issues

#### Issue: API Server Won't Start
**Symptoms:**
- `uvicorn` command fails
- Port already in use errors
- Import errors

**Solutions:**
```bash
# Check if port is in use
netstat -an | grep :8000
# or
lsof -i :8000

# Kill existing process
kill -9 $(lsof -t -i:8000)

# Check Python dependencies
pip list | grep -E "(fastapi|litellm|uvicorn)"

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Start with different port
uvicorn main:app --host 0.0.0.0 --port 8001
```

#### Issue: Cannot Connect to API
**Symptoms:**
- Connection refused errors
- Timeout errors
- DNS resolution failures

**Solutions:**
```bash
# Check if server is running
curl -v http://localhost:8000/health

# Check firewall settings
sudo ufw status
sudo iptables -L

# Test with different host/port
curl -v http://127.0.0.1:8000/health
curl -v http://0.0.0.0:8000/health

# Check Docker container (if applicable)
docker ps
docker logs container_name
```

### 2. Authentication and Authorization Issues

#### Issue: Azure OpenAI Authentication Failed
**Symptoms:**
- 401 Unauthorized errors
- "Invalid API key" messages
- Authentication timeout

**Error Response:**
```json
{
  "detail": "Authentication failed",
  "error_code": "AUTHENTICATION_FAILED",
  "model_attempted": "azure/gpt-4o",
  "endpoint_attempted": "westeurope"
}
```

**Solutions:**
```bash
# Verify environment variables
env | grep AZURE_API_KEY

# Test API key directly with Azure
curl -X POST "https://your-endpoint.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-05-01-preview" \
  -H "Content-Type: application/json" \
  -H "api-key: YOUR_API_KEY" \
  -d '{"messages":[{"role":"user","content":"test"}],"max_tokens":10}'

# Check key format (should be 32 characters)
echo $AZURE_API_KEY_OTHER | wc -c

# Regenerate API keys in Azure portal if needed
```

#### Issue: Helicone Authentication Failed
**Symptoms:**
- Helicone headers rejected
- Monitoring data not appearing
- "Invalid Helicone API key" errors

**Solutions:**
```bash
# Verify Helicone API key
curl -X GET "https://api.helicone.ai/v1/request" \
  -H "Authorization: Bearer YOUR_HELICONE_KEY"

# Check environment variable
echo $HELICONE_API_KEY

# Test without Helicone headers
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{"data": "test", "operation": "content_generation"}'
```

### 3. Model and Endpoint Issues

#### Issue: Model Unavailable
**Symptoms:**
- "Model not found" errors
- Specific models returning 404
- Fallback models being used unexpectedly

**Error Response:**
```json
{
  "detail": "Model azure/gpt-4.1 is temporarily unavailable",
  "error_code": "MODEL_UNAVAILABLE",
  "available_alternatives": ["azure/gpt-4o", "azure/deepseek"]
}
```

**Solutions:**
```bash
# Check available models
curl -X GET "http://localhost:8000/models/available"

# Test specific model
curl -X POST "http://localhost:8000/config/test-model" \
  -H "Content-Type: application/json" \
  -d '{"model": "azure/gpt-4.1", "test_prompt": "Hello"}'

# Check Azure deployment names
# Verify deployment names match environment variables
echo "GPT-4.1 Deployment: $GPT_4_1_DEPLOYMENT"
echo "O1 Deployment: $O1_DEPLOYMENT"

# Check endpoint health
curl -X GET "http://localhost:8000/health/endpoints"
```

#### Issue: Rate Limiting
**Symptoms:**
- 429 Too Many Requests errors
- Requests being throttled
- Unexpected delays

**Error Response:**
```json
{
  "detail": "Rate limit exceeded",
  "error_code": "RATE_LIMIT_EXCEEDED",
  "retry_after": 60,
  "current_usage": "95/100 requests per minute"
}
```

**Solutions:**
```python
# Implement exponential backoff
import time
import random

def retry_with_backoff(func, max_retries=5):
    for attempt in range(max_retries):
        try:
            return func()
        except RateLimitError as e:
            if attempt == max_retries - 1:
                raise
            
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            print(f"Rate limited. Waiting {wait_time:.2f}s...")
            time.sleep(wait_time)

# Use cheaper models for high-volume tasks
# Switch from azure/gpt-4.1 to azure/gpt-4o or azure/gpt-35-turbo

# Implement request queuing
from queue import Queue
import threading

request_queue = Queue()

def process_requests():
    while True:
        request = request_queue.get()
        try:
            # Process with delay
            time.sleep(0.1)  # 100ms between requests
            result = process_request(request)
        except Exception as e:
            print(f"Request failed: {e}")
        finally:
            request_queue.task_done()
```

### 4. Performance Issues

#### Issue: Slow Response Times
**Symptoms:**
- Requests taking >10 seconds
- Timeouts occurring
- Poor user experience

**Diagnostic Commands:**
```bash
# Check response times
time curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{"data": "test", "operation": "content_generation"}'

# Monitor system resources
top
htop
iostat 1

# Check network latency to Azure
ping eastus2.api.cognitive.microsoft.com
ping westeurope.api.cognitive.microsoft.com
```

**Solutions:**
```python
# Use faster models for time-sensitive requests
fast_models = ["azure/gpt-35-turbo", "azure/gpt-4o-mini"]

# Implement timeout handling
import requests

def api_call_with_timeout(url, data, timeout=30):
    try:
        response = requests.post(url, json=data, timeout=timeout)
        return response.json()
    except requests.Timeout:
        print("Request timed out. Consider using a faster model.")
        raise

# Enable caching for repeated requests
# Use Helicone caching or implement local caching

# Optimize prompt length
def optimize_prompt(prompt, max_length=2000):
    if len(prompt) > max_length:
        return prompt[:max_length] + "..."
    return prompt
```

#### Issue: High Memory Usage
**Symptoms:**
- Server running out of memory
- OOM (Out of Memory) errors
- Slow garbage collection

**Solutions:**
```bash
# Monitor memory usage
free -h
ps aux | grep python

# Restart server periodically
# Implement memory monitoring
# Use process managers like supervisor or systemd

# Optimize Python settings
export PYTHONOPTIMIZE=1
export PYTHONDONTWRITEBYTECODE=1

# Limit concurrent requests
uvicorn main:app --workers 2 --limit-concurrency 100
```

### 5. Data and Content Issues

#### Issue: Content Filtering
**Symptoms:**
- Requests blocked by safety filters
- "Content filtered" errors
- Unexpected empty responses

**Error Response:**
```json
{
  "detail": "Content blocked by safety filters",
  "error_code": "CONTENT_FILTERED",
  "filter_categories": ["hate", "violence"],
  "suggestions": ["Modify content to be less explicit", "Use different phrasing"]
}
```

**Solutions:**
```python
# Implement content preprocessing
import re

def sanitize_content(text):
    # Remove potentially problematic content
    text = re.sub(r'\b(explicit_word1|explicit_word2)\b', '[REDACTED]', text, flags=re.IGNORECASE)
    
    # Replace with safer alternatives
    replacements = {
        'kill': 'stop',
        'destroy': 'remove',
        'attack': 'criticize'
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text

# Use different models that may be less restrictive
# Try azure/llama-3.3 or azure/deepseek for technical content

# Implement fallback content generation
def generate_with_fallback(prompt):
    try:
        return generate_content(prompt)
    except ContentFilterError:
        sanitized_prompt = sanitize_content(prompt)
        return generate_content(sanitized_prompt)
```

#### Issue: Inconsistent Output Quality
**Symptoms:**
- Variable response quality
- Unexpected formatting
- Inconsistent tone or style

**Solutions:**
```python
# Use consistent temperature settings
# Lower temperature (0.1-0.3) for consistent output
# Higher temperature (0.7-0.9) for creative variety

# Implement output validation
def validate_output(response, expected_format=None):
    if not response or len(response.strip()) < 10:
        raise ValueError("Response too short")
    
    if expected_format == "json":
        try:
            json.loads(response)
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format")
    
    return response

# Use system prompts for consistency
system_prompt = """
You are a professional content writer. Always:
- Use clear, concise language
- Maintain a professional tone
- Structure content with proper headings
- Include relevant examples
"""

# Implement quality scoring
def score_response_quality(response):
    score = 0
    
    # Length check
    if 100 <= len(response) <= 2000:
        score += 25
    
    # Structure check
    if any(marker in response for marker in ['##', '**', '1.', '-']):
        score += 25
    
    # Completeness check
    if response.strip().endswith(('.', '!', '?')):
        score += 25
    
    # Relevance check (implement based on your needs)
    score += 25
    
    return score
```

### 6. Monitoring and Logging Issues

#### Issue: Helicone Data Not Appearing
**Symptoms:**
- No requests in Helicone dashboard
- Missing performance metrics
- Incomplete logging

**Solutions:**
```bash
# Check Helicone configuration
curl -X GET "http://localhost:8000/config/validate" | jq '.helicone_integration'

# Verify headers are being sent
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -H "Helicone-Auth: Bearer YOUR_KEY" \
  -H "Helicone-Property-Test: true" \
  -d '{"data": "test", "operation": "content_generation"}' \
  -v  # Verbose output to see headers

# Check Helicone API directly
curl -X GET "https://api.helicone.ai/v1/request" \
  -H "Authorization: Bearer YOUR_HELICONE_KEY" \
  -H "Content-Type: application/json"

# Enable debug logging
export HELICONE_DEBUG=true
export LOG_LEVEL=DEBUG
```

#### Issue: Missing Application Logs
**Symptoms:**
- No log files generated
- Missing error information
- Difficult to debug issues

**Solutions:**
```python
# Configure proper logging
import logging
from logging.handlers import RotatingFileHandler

# Setup file logging
file_handler = RotatingFileHandler(
    'app.log', 
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)
file_handler.setLevel(logging.INFO)

# Setup console logging
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[file_handler, console_handler]
)

# Add request ID tracking
import uuid

def add_request_id():
    request_id = str(uuid.uuid4())
    logging.getLogger().addFilter(
        lambda record: setattr(record, 'request_id', request_id) or True
    )
    return request_id
```

## 🔍 Debugging Strategies

### 1. Systematic Debugging Approach

#### Step 1: Isolate the Problem
```bash
# Test basic connectivity
curl -X GET "http://localhost:8000/health"

# Test specific endpoint
curl -X GET "http://localhost:8000/models/available"

# Test with minimal payload
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{"data": "test", "operation": "content_generation", "parameters": {"model": "azure/gpt-35-turbo"}}'
```

#### Step 2: Check Configuration
```python
# Configuration validation script
import os
import requests

def validate_configuration():
    issues = []
    
    # Check required environment variables
    required_vars = [
        'AZURE_API_KEY_4_1_O1',
        'AZURE_API_BASE_4_1_O1',
        'AZURE_API_KEY_OTHER',
        'AZURE_API_BASE_OTHER'
    ]
    
    for var in required_vars:
        if not os.getenv(var):
            issues.append(f"Missing environment variable: {var}")
    
    # Test API connectivity
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            issues.append(f"Health check failed: {response.status_code}")
    except Exception as e:
        issues.append(f"Cannot connect to API: {e}")
    
    return issues

# Run validation
issues = validate_configuration()
if issues:
    print("Configuration issues found:")
    for issue in issues:
        print(f"- {issue}")
else:
    print("Configuration looks good!")
```

#### Step 3: Enable Verbose Logging
```python
# Enable debug mode
import logging
import sys

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    stream=sys.stdout
)

# Enable HTTP request logging
import http.client as http_client
http_client.HTTPConnection.debuglevel = 1

# Enable LiteLLM debug logging
import litellm
litellm.set_verbose = True
```

### 2. Performance Debugging

#### Monitor Response Times
```python
import time
import statistics

def benchmark_api_calls(client, prompts, model="azure/gpt-4o"):
    """Benchmark API performance with multiple calls."""
    response_times = []
    errors = []
    
    for i, prompt in enumerate(prompts):
        start_time = time.time()
        try:
            result = client.generate_content(prompt, model=model)
            response_time = time.time() - start_time
            response_times.append(response_time)
            print(f"Call {i+1}: {response_time:.2f}s - Success")
        except Exception as e:
            response_time = time.time() - start_time
            errors.append((i+1, str(e), response_time))
            print(f"Call {i+1}: {response_time:.2f}s - Error: {e}")
    
    if response_times:
        print(f"\nPerformance Summary:")
        print(f"Average: {statistics.mean(response_times):.2f}s")
        print(f"Median: {statistics.median(response_times):.2f}s")
        print(f"Min: {min(response_times):.2f}s")
        print(f"Max: {max(response_times):.2f}s")
    
    if errors:
        print(f"\nErrors: {len(errors)}/{len(prompts)}")
        for call_num, error, time_taken in errors:
            print(f"Call {call_num}: {error} (after {time_taken:.2f}s)")

# Usage
test_prompts = [
    "Write a short paragraph about AI",
    "Explain machine learning briefly",
    "Describe cloud computing",
    "What is data science?",
    "Define artificial intelligence"
]

benchmark_api_calls(client, test_prompts)
```

### 3. Error Pattern Analysis

#### Log Analysis Script
```python
import re
from collections import Counter
from datetime import datetime

def analyze_error_logs(log_file_path):
    """Analyze error patterns in log files."""
    error_patterns = []
    timestamps = []
    
    with open(log_file_path, 'r') as f:
        for line in f:
            if 'ERROR' in line:
                # Extract timestamp
                timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if timestamp_match:
                    timestamps.append(timestamp_match.group(1))
                
                # Extract error type
                if 'Rate limit' in line:
                    error_patterns.append('rate_limit')
                elif 'Authentication' in line:
                    error_patterns.append('authentication')
                elif 'Model unavailable' in line:
                    error_patterns.append('model_unavailable')
                elif 'Timeout' in line:
                    error_patterns.append('timeout')
                else:
                    error_patterns.append('other')
    
    # Analyze patterns
    error_counts = Counter(error_patterns)
    print("Error Pattern Analysis:")
    for error_type, count in error_counts.most_common():
        print(f"- {error_type}: {count} occurrences")
    
    # Time-based analysis
    if timestamps:
        print(f"\nTime Range: {timestamps[0]} to {timestamps[-1]}")
        print(f"Total Errors: {len(timestamps)}")

# Usage
# analyze_error_logs('app.log')
```

## 📞 Getting Help

### 1. Self-Service Resources
- **Documentation**: Check [API Reference](api_reference.md) for endpoint details
- **Model Guide**: Review [Model Guide](model_guide.md) for model selection
- **Integration Examples**: See [Integration Guide](integration_guide.md) for code samples

### 2. Diagnostic Information to Collect
When reporting issues, include:

```bash
# System information
uname -a
python --version
pip list | grep -E "(fastapi|litellm|uvicorn|helicone)"

# API status
curl -X GET "http://localhost:8000/health"
curl -X GET "http://localhost:8000/config/validate"

# Recent logs
tail -n 50 app.log

# Environment variables (sanitized)
env | grep -E "(AZURE|HELICONE)" | sed 's/=.*/=***/'
```

### 3. Community Support
- **GitHub Issues**: Report bugs and feature requests
- **Discord/Slack**: Join community discussions
- **Stack Overflow**: Search for similar issues

### 4. Professional Support
For production deployments:
- **Azure Support**: For Azure OpenAI specific issues
- **Helicone Support**: For monitoring and observability issues
- **Custom Support**: For application-specific problems
