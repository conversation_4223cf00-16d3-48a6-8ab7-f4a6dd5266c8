# API Reference

Complete documentation for the Enhanced FastAPI application with LiteLLM Azure OpenAI integration and Helicone monitoring.

## 🔗 Base URL
```
http://localhost:8000
```

## 🔐 Authentication

### Azure OpenAI API Keys
The application uses Azure OpenAI credentials configured via environment variables:
- `AZURE_API_KEY_4_1_O1` - For advanced models (East US 2)
- `AZURE_API_KEY_STANDARD` - For standard models (West Europe)

### Helicone Integration
Optional monitoring headers:
```http
Helicone-Auth: Bearer YOUR_HELICONE_API_KEY
Helicone-Cache-Enabled: true
Helicone-User-Id: your-user-id
```

## 📊 Response Format

All endpoints return JSON responses with consistent structure:

### Success Response
```json
{
  "result": "...",
  "status": "success", 
  "message": "Operation completed successfully",
  "metadata": {
    "model_used": "azure/gpt-4o",
    "tokens_used": 150,
    "response_time": 1.23,
    "endpoint": "westeurope"
  }
}
```

### Error Response
```json
{
  "detail": "Error description",
  "error_code": "MODEL_UNAVAILABLE",
  "timestamp": "2025-06-29T10:30:00Z",
  "request_id": "req_123456"
}
```

## 🚀 Content Generation Endpoints

### POST /process
Primary endpoint for content processing with intelligent model routing.

**Request Body:**
```json
{
  "data": "Content to process",
  "operation": "content_generation",
  "parameters": {
    "model": "azure/gpt-4o",
    "max_tokens": 1000,
    "temperature": 0.7,
    "use_case": "marketing"
  }
}
```

**Response:**
```json
{
  "result": "Generated content...",
  "status": "success",
  "message": "Operation 'content_generation' completed successfully",
  "metadata": {
    "model_used": "azure/gpt-4o",
    "tokens_used": 245,
    "response_time": 2.1,
    "endpoint": "westeurope",
    "cost_estimate": 0.0012
  }
}
```

**Supported Operations:**
- `content_generation` - General content creation
- `content_rewriting` - Text improvement and rewriting
- `creative_writing` - Creative content generation
- `technical_analysis` - Technical documentation
- `summarization` - Content summarization
- `translation` - Language translation

### POST /process (Content Repurposing)
Repurpose existing content to be authentically human-written while maintaining similar length.

**Request Body:**
```json
{
  "data": {
    "content": "Original content to repurpose",
    "tone": "professional",
    "target_audience": "business_professionals",
    "model_name": "azure/gpt-4o"
  },
  "operation": "repurpose_content"
}
```

**Response:**
```json
{
  "result": [
    {
      "text": "Repurposed content that sounds authentically human-written..."
    }
  ],
  "status": "success",
  "message": "Content repurposed successfully"
}
```

**Response:**
```json
{
  "variants": [
    {
      "style": "professional",
      "content": "Professional variant...",
      "score": 8.5,
      "metrics": {
        "readability": 9.0,
        "engagement": 8.0,
        "seo_score": 8.5
      }
    }
  ],
  "recommended_variant": 0,
  "comparison_analysis": "Detailed analysis...",
  "metadata": {
    "total_variants": 3,
    "processing_time": 4.2,
    "models_used": ["azure/gpt-4.1"]
  }
}
```

## 🤖 Model Management Endpoints

### GET /models/available
List all available models with their current status.

**Response:**
```json
{
  "models": [
    {
      "name": "azure/gpt-4.1",
      "endpoint": "eastus2",
      "status": "healthy",
      "capabilities": ["advanced_reasoning", "complex_tasks"],
      "cost_tier": "premium",
      "max_tokens": 128000,
      "use_cases": ["technical_analysis", "complex_reasoning"]
    },
    {
      "name": "azure/gpt-4o",
      "endpoint": "westeurope", 
      "status": "healthy",
      "capabilities": ["general_purpose", "fast_response"],
      "cost_tier": "standard",
      "max_tokens": 128000,
      "use_cases": ["content_generation", "conversation"]
    }
  ],
  "total_models": 8,
  "healthy_models": 8,
  "endpoint_distribution": {
    "eastus2": 2,
    "westeurope": 6
  }
}
```

### GET /models/recommended/{use_case}
Get model recommendation for specific use case.

**Path Parameters:**
- `use_case`: One of `content_rewriting`, `creative_writing`, `technical_analysis`, `simple_tasks`, `conversation`, `code_generation`, `summarization`, `translation`

**Response:**
```json
{
  "use_case": "creative_writing",
  "recommended_model": "azure/grok-3",
  "model_info": {
    "name": "azure/grok-3",
    "endpoint": "westeurope",
    "capabilities": ["creative_tasks", "storytelling"],
    "cost_tier": "standard",
    "reasoning": "Optimized for creative content generation with strong storytelling capabilities"
  },
  "alternatives": [
    {
      "model": "azure/gpt-4o",
      "reason": "General purpose alternative with good creative capabilities"
    }
  ],
  "available_use_cases": [
    "content_rewriting", "creative_writing", "technical_analysis",
    "simple_tasks", "conversation", "code_generation", 
    "summarization", "translation"
  ]
}
```

### POST /models/select-optimal
Intelligent model selection based on request characteristics.

**Request Body:**
```json
{
  "task_description": "Write a technical blog post about machine learning",
  "content_length": "long",
  "complexity": "high",
  "budget_priority": "balanced",
  "response_time_priority": "medium"
}
```

**Response:**
```json
{
  "selected_model": "azure/gpt-4.1",
  "confidence": 0.92,
  "reasoning": "High complexity technical content requires advanced reasoning capabilities",
  "alternatives": [
    {
      "model": "azure/deepseek",
      "score": 0.85,
      "reason": "Good for technical content, lower cost"
    }
  ],
  "estimated_cost": 0.024,
  "estimated_time": 3.5,
  "optimization_factors": {
    "complexity_match": 0.95,
    "cost_efficiency": 0.80,
    "speed": 0.75
  }
}
```

### POST /models/compare
Compare performance across multiple models for the same task.

**Request Body:**
```json
{
  "prompt": "Explain quantum computing in simple terms",
  "models": ["azure/gpt-4o", "azure/gpt-4.1", "azure/deepseek"],
  "evaluation_criteria": ["accuracy", "clarity", "engagement"],
  "include_cost_analysis": true
}
```

**Response:**
```json
{
  "comparison_results": [
    {
      "model": "azure/gpt-4.1",
      "response": "Quantum computing explanation...",
      "scores": {
        "accuracy": 9.2,
        "clarity": 8.8,
        "engagement": 8.5
      },
      "overall_score": 8.83,
      "cost": 0.0045,
      "response_time": 2.3
    }
  ],
  "winner": "azure/gpt-4.1",
  "summary": "GPT-4.1 provided the most accurate and clear explanation",
  "cost_analysis": {
    "most_cost_effective": "azure/gpt-4o",
    "best_value": "azure/gpt-4.1"
  }
}
```

## 📊 Monitoring & Analytics Endpoints

### GET /monitoring/summary
Comprehensive monitoring overview with Helicone integration.

**Response:**
```json
{
  "metrics": {
    "total_calls": {
      "azure/gpt-4o": 1250,
      "azure/gpt-4.1": 340,
      "azure/grok-3": 180
    },
    "success_rates": {
      "azure/gpt-4o": 0.98,
      "azure/gpt-4.1": 0.96,
      "azure/grok-3": 0.99
    },
    "average_response_times": {
      "azure/gpt-4o": 1.8,
      "azure/gpt-4.1": 3.2,
      "azure/grok-3": 2.1
    },
    "health_scores": {
      "azure/gpt-4o": 95.2,
      "azure/gpt-4.1": 92.8,
      "azure/grok-3": 97.1
    }
  },
  "helicone_data": {
    "total_requests": 1770,
    "total_cost": 12.45,
    "cache_hit_rate": 0.23,
    "average_latency": 2.1
  },
  "health": {
    "overall_status": "healthy",
    "endpoint_health": {
      "eastus2": true,
      "westeurope": true
    }
  },
  "recent_errors": {
    "azure/gpt-4o": [],
    "azure/gpt-4.1": [
      {
        "timestamp": "2025-06-29T10:25:00Z",
        "error": "Rate limit exceeded",
        "category": "rate_limit"
      }
    ]
  }
}
```

### GET /analytics/performance-comparison
Detailed performance analytics across models and endpoints.

**Query Parameters:**
- `time_range`: `1h`, `24h`, `7d`, `30d` (default: `24h`)
- `models`: Comma-separated list of models to include
- `include_costs`: Boolean to include cost analysis

**Response:**
```json
{
  "time_range": "24h",
  "performance_metrics": {
    "azure/gpt-4o": {
      "total_requests": 450,
      "success_rate": 0.98,
      "avg_response_time": 1.8,
      "p95_response_time": 3.2,
      "total_cost": 2.34,
      "cost_per_request": 0.0052,
      "endpoint": "westeurope"
    },
    "azure/gpt-4.1": {
      "total_requests": 120,
      "success_rate": 0.96,
      "avg_response_time": 3.2,
      "p95_response_time": 5.8,
      "total_cost": 4.56,
      "cost_per_request": 0.038,
      "endpoint": "eastus2"
    }
  },
  "trends": {
    "request_volume": "increasing",
    "success_rate": "stable",
    "response_time": "improving"
  },
  "recommendations": [
    "Consider using gpt-4o for high-volume simple tasks",
    "Reserve gpt-4.1 for complex reasoning tasks",
    "Enable Helicone caching to reduce costs"
  ]
}
```

### POST /monitoring/health-check
Manually trigger health checks for all endpoints.

**Response:**
```json
{
  "message": "Health checks completed",
  "timestamp": **********.123,
  "results": {
    "eastus2": true,
    "westeurope": true,
    "legacy": false
  },
  "detailed_results": {
    "eastus2": {
      "status": "healthy",
      "response_time": 0.8,
      "models_available": ["azure/gpt-4.1", "azure/o1"],
      "last_error": null
    },
    "westeurope": {
      "status": "healthy",
      "response_time": 0.6,
      "models_available": ["azure/gpt-4o", "azure/grok-3", "azure/llama-3.3", "azure/deepseek", "azure/gpt-4o-mini", "azure/gpt-35-turbo"],
      "last_error": null
    }
  }
}
```

## 🏥 Health Check Endpoints

### GET /health
General application health status with model information.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": **********.123,
  "version": "2.0.0",
  "service": "Enhanced Data Processing API",
  "endpoints_healthy": true,
  "available_models": 8,
  "endpoint_status": {
    "eastus2": true,
    "westeurope": true
  },
  "features": [
    "Multi-model Azure OpenAI support",
    "Intelligent model routing",
    "Cost optimization",
    "Fallback mechanisms",
    "Creative and technical content generation",
    "Model comparison capabilities",
    "Helicone monitoring integration"
  ],
  "helicone_status": {
    "enabled": true,
    "last_sync": "2025-06-29T10:30:00Z",
    "cache_enabled": true
  }
}
```

### GET /health/endpoints
Detailed health status of all Azure endpoints.

**Response:**
```json
{
  "overall_health": "healthy",
  "endpoints": {
    "eastus2": {
      "healthy": true,
      "name": "East US 2 - Advanced Models",
      "models": ["azure/gpt-4.1", "azure/o1"],
      "priority": 1,
      "cost_tier": "premium",
      "response_time": 0.8,
      "last_check": "2025-06-29T10:30:00Z"
    },
    "westeurope": {
      "healthy": true,
      "name": "West Europe - Standard Models",
      "models": ["azure/gpt-4o", "azure/grok-3", "azure/llama-3.3", "azure/deepseek", "azure/gpt-4o-mini", "azure/gpt-35-turbo"],
      "priority": 2,
      "cost_tier": "standard",
      "response_time": 0.6,
      "last_check": "2025-06-29T10:30:00Z"
    }
  },
  "summary": {
    "total_endpoints": 2,
    "healthy_endpoints": 2,
    "total_models": 8,
    "available_models": 8
  }
}
```

## ⚙️ Configuration & Validation Endpoints

### GET /config/validate
Validate current configuration and environment setup.

**Response:**
```json
{
  "configuration_valid": true,
  "environment_variables": {
    "azure_credentials": "configured",
    "helicone_integration": "configured",
    "mongodb_connection": "configured"
  },
  "model_configurations": {
    "total_configured": 8,
    "valid_configurations": 8,
    "invalid_configurations": 0
  },
  "endpoint_connectivity": {
    "eastus2": "connected",
    "westeurope": "connected"
  },
  "warnings": [],
  "errors": []
}
```

### POST /config/test-model
Test specific model configuration and connectivity.

**Request Body:**
```json
{
  "model": "azure/gpt-4o",
  "test_prompt": "Hello, world!",
  "include_performance_test": true
}
```

**Response:**
```json
{
  "model": "azure/gpt-4o",
  "test_successful": true,
  "response": "Hello! How can I assist you today?",
  "performance_metrics": {
    "response_time": 1.2,
    "tokens_used": 12,
    "cost": 0.00024
  },
  "endpoint_info": {
    "endpoint": "westeurope",
    "api_version": "2024-05-01-preview",
    "deployment": "gpt-4o"
  },
  "helicone_logged": true
}
```

## 🚨 Error Codes & Handling

### HTTP Status Codes

| Code | Description | Common Causes |
|------|-------------|---------------|
| 200 | Success | Request completed successfully |
| 400 | Bad Request | Invalid request format or parameters |
| 401 | Unauthorized | Missing or invalid API credentials |
| 403 | Forbidden | Insufficient permissions or quota exceeded |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server-side processing error |
| 503 | Service Unavailable | All models/endpoints unavailable |

### Error Response Format

```json
{
  "detail": "Detailed error description",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2025-06-29T10:30:00Z",
  "request_id": "req_123456",
  "model_attempted": "azure/gpt-4o",
  "endpoint_attempted": "westeurope",
  "retry_after": 30,
  "suggestions": [
    "Try using a different model",
    "Reduce request complexity",
    "Check API quotas"
  ]
}
```

### Common Error Codes

- `MODEL_UNAVAILABLE` - Requested model is temporarily unavailable
- `RATE_LIMIT_EXCEEDED` - API rate limit reached
- `QUOTA_EXCEEDED` - Monthly quota limit reached
- `INVALID_MODEL` - Specified model doesn't exist
- `AUTHENTICATION_FAILED` - Invalid API credentials
- `CONTENT_FILTERED` - Content blocked by safety filters
- `TOKEN_LIMIT_EXCEEDED` - Request exceeds model's token limit
- `ENDPOINT_TIMEOUT` - Request timed out
- `HELICONE_ERROR` - Monitoring service error (non-blocking)

## 🔧 Model Selection Decision Matrix

| Use Case | Primary Model | Alternative | Reasoning |
|----------|---------------|-------------|-----------|
| **Creative Writing** | `azure/grok-3` | `azure/gpt-4o` | Optimized for creative tasks |
| **Technical Analysis** | `azure/gpt-4.1` | `azure/deepseek` | Advanced reasoning capabilities |
| **Content Rewriting** | `azure/gpt-4o` | `azure/gpt-4o-mini` | Balanced performance/cost |
| **Simple Tasks** | `azure/gpt-35-turbo` | `azure/gpt-4o-mini` | Fast and cost-effective |
| **Code Generation** | `azure/deepseek` | `azure/gpt-4.1` | Specialized for technical content |
| **Conversation** | `azure/gpt-4o` | `azure/gpt-35-turbo` | Natural dialogue capabilities |
| **Summarization** | `azure/gpt-4o-mini` | `azure/gpt-35-turbo` | Efficient for shorter outputs |
| **Translation** | `azure/gpt-4o` | `azure/llama-3.3` | Strong multilingual support |

## 📈 Performance Benchmarks

### Response Time Averages (seconds)
- `azure/gpt-35-turbo`: 0.8s
- `azure/gpt-4o-mini`: 1.2s
- `azure/gpt-4o`: 1.8s
- `azure/grok-3`: 2.1s
- `azure/llama-3.3`: 2.3s
- `azure/deepseek`: 2.8s
- `azure/gpt-4.1`: 3.2s
- `azure/o1`: 4.5s

### Cost Efficiency (per 1K tokens)
- `azure/gpt-35-turbo`: $0.0015
- `azure/gpt-4o-mini`: $0.0003
- `azure/gpt-4o`: $0.005
- `azure/grok-3`: $0.004
- `azure/llama-3.3`: $0.003
- `azure/deepseek`: $0.0025
- `azure/gpt-4.1`: $0.03
- `azure/o1`: $0.06

## 🔗 Helicone Integration Headers

### Required Headers
```http
Helicone-Auth: Bearer YOUR_HELICONE_API_KEY
```

### Optional Headers
```http
Helicone-Cache-Enabled: true
Helicone-User-Id: user-123
Helicone-Session-Id: session-456
Helicone-Session-Name: "Content Generation Session"
Helicone-Property-Environment: "production"
Helicone-Property-Model-Type: "azure-openai"
Helicone-Retry-Enabled: true
Helicone-Retry-Count: 3
```

### Custom Properties
```http
Helicone-Property-Use-Case: "marketing-content"
Helicone-Property-Content-Type: "blog-post"
Helicone-Property-User-Tier: "premium"
Helicone-Property-Cost-Center: "marketing"
```
