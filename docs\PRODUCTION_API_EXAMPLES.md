# Production API Examples

## Overview

This document provides working API examples for both the Opinion Generator and Content Summarizer modules after removing all hardcoded URLs and making them production-ready.

## Production Readiness Changes

### ✅ Removed Hardcoded URLs
- **Opinion Generator**: Removed hardcoded test URL from `test_opinion_generator()` function
- **Content Summarizer**: Removed hardcoded test URL from `test_content_summarizer()` function
- **Clean Production Code**: No test functions or development-specific content in production modules

### ✅ Module Structure
Both modules now contain only production-ready code:
- Core functionality functions
- Validation functions
- No test functions or hardcoded URLs
- Clean, professional structure

## API Endpoints

### Base URL
```
http://localhost:8000/process
```

### Headers
```
Content-Type: application/json
```

## Opinion Generator API

### Endpoint
`POST /process` with `operation: "opinion_generator"`

### Required Parameters
- `url`: Article URL to analyze
- `stance`: "agree" or "disagree"

### Optional Parameters
- `tone`: Tone of voice (default: "professional")
- `platform`: Target platform (default: "general")
- `model_name`: Azure OpenAI model to use

### Working cURL Examples

#### Example 1: Professional Agree Opinion for LinkedIn
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://www.bbc.com/news/technology",
      "stance": "agree",
      "tone": "professional",
      "platform": "linkedin",
      "model_name": "azure/gpt-4o"
    },
    "operation": "opinion_generator"
  }'
```

#### Example 2: Casual Disagree Opinion for Twitter
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://www.reuters.com/technology/",
      "stance": "disagree",
      "tone": "casual",
      "platform": "twitter"
    },
    "operation": "opinion_generator"
  }'
```

#### Example 3: Passionate Agree Opinion for Facebook
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://techcrunch.com/",
      "stance": "agree",
      "tone": "passionate",
      "platform": "facebook",
      "model_name": "azure/grok-3"
    },
    "operation": "opinion_generator"
  }'
```

### Response Format
```json
{
  "result": [
    {
      "text": "Generated opinion post content ready for social media..."
    }
  ],
  "status": "success",
  "message": "Operation 'opinion_generator' completed successfully"
}
```

## Content Summarizer API

### Endpoint
`POST /process` with `operation: "create_summary"`

### Required Parameters
- `url`: Article URL to summarize

### Optional Parameters
- `summary_length`: "short", "medium", or "long" (default: "medium")
- `tone`: Tone for summary (default: "neutral")
- `platform`: Target platform (default: "general")
- `model_name`: Azure OpenAI model to use

### Working cURL Examples

#### Example 1: Short Summary for Twitter
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://www.bbc.com/news/technology",
      "summary_length": "short",
      "tone": "neutral",
      "platform": "twitter"
    },
    "operation": "create_summary"
  }'
```

#### Example 2: Medium Summary for LinkedIn
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://www.reuters.com/technology/",
      "summary_length": "medium",
      "tone": "professional",
      "platform": "linkedin",
      "model_name": "azure/gpt-4o"
    },
    "operation": "create_summary"
  }'
```

#### Example 3: Long Summary for General Use
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://techcrunch.com/",
      "summary_length": "long",
      "tone": "informative",
      "platform": "general",
      "model_name": "azure/llama-3.3"
    },
    "operation": "create_summary"
  }'
```

#### Example 4: Default Parameters (Medium Summary)
```bash
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "url": "https://www.wired.com/"
    },
    "operation": "create_summary"
  }'
```

### Response Format
```json
{
  "result": [
    {
      "text": "Generated summary content ready for use..."
    }
  ],
  "status": "success",
  "message": "Operation 'create_summary' completed successfully"
}
```

## Python Client Examples

### Opinion Generator Client
```python
import requests

def generate_opinion(url, stance, tone="professional", platform="general", model_name=None):
    payload = {
        "data": {
            "url": url,
            "stance": stance,
            "tone": tone,
            "platform": platform
        },
        "operation": "opinion_generator"
    }
    
    if model_name:
        payload["data"]["model_name"] = model_name
    
    response = requests.post(
        "http://localhost:8000/process",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        return result["result"][0]["text"]
    else:
        raise Exception(f"API call failed: {response.text}")

# Usage examples
opinion_post = generate_opinion(
    url="https://www.bbc.com/news/technology",
    stance="agree",
    tone="professional",
    platform="linkedin"
)
print(opinion_post)
```

### Content Summarizer Client
```python
import requests

def create_summary(url, summary_length="medium", tone="neutral", platform="general", model_name=None):
    payload = {
        "data": {
            "url": url,
            "summary_length": summary_length,
            "tone": tone,
            "platform": platform
        },
        "operation": "create_summary"
    }
    
    if model_name:
        payload["data"]["model_name"] = model_name
    
    response = requests.post(
        "http://localhost:8000/process",
        json=payload,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        return result["result"][0]["text"]
    else:
        raise Exception(f"API call failed: {response.text}")

# Usage examples
short_summary = create_summary(
    url="https://www.reuters.com/technology/",
    summary_length="short",
    platform="twitter"
)

long_summary = create_summary(
    url="https://techcrunch.com/",
    summary_length="long",
    tone="informative",
    platform="general"
)

print("Short Summary:", short_summary)
print("Long Summary:", long_summary)
```

## Error Handling

### Common Error Responses

#### Missing Required Fields
```json
{
  "detail": "Missing required field: 'url'",
  "status_code": 400
}
```

#### Invalid Parameters
```json
{
  "detail": "Invalid stance: 'neutral'. Must be one of: agree, disagree",
  "status_code": 400
}
```

#### URL Access Issues
```json
{
  "detail": "URL fetching failed: Connection timeout",
  "status_code": 500
}
```

## Testing

### Run Production API Tests
```bash
python test_production_api.py
```

This script will:
- Test both API endpoints
- Provide detailed output
- Show working cURL examples
- Verify production readiness

### Manual Testing
1. Start the server: `python main.py`
2. Use any of the cURL examples above
3. Replace URLs with actual articles you want to process
4. Verify the responses are clean and ready-to-use

## Production Deployment Notes

### ✅ Production Ready Features
- No hardcoded URLs or test data
- Clean module structure
- Comprehensive error handling
- Proper input validation
- Authentic human-written output
- Platform optimization
- Model flexibility

### ✅ Security Considerations
- Input validation for all parameters
- URL format validation
- Timeout handling for external requests
- Graceful error handling
- No sensitive data in code

### ✅ Performance Features
- Retry logic for network issues
- Efficient content extraction
- Optimized model parameters
- Helicone monitoring integration
- Proper logging and analytics

Both APIs are now completely production-ready and can be deployed without any development-specific code or hardcoded test data.
